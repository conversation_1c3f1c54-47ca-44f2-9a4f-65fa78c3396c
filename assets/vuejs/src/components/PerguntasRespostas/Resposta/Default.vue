<template>
    <div>
        <loading 
            :active.sync="isLoading" 
            :is-full-page="fullPage"
        >
        </loading>

        <div class="row">
            <div class="col-md-4">
                <label for="nome" class="d-block">
                    Partnumbers
                </label>
                <v-select 
                v-model="this.haspartnumbers"
                    @input="handleSelect" 
                    placeholder="Selecione"
                    :multiple="true"
                    :options="getOptions()"
                >
                    <div slot="no-options" name="no-options">Não encontramos nenhuma opção.</div>
                </v-select>
            </div>
        </div>
        
        <div class="row mt-5" >
            <div class="col-md-12" v-for="(item, index) in perguntas" :key="item.id">
                    <Item
                        :permissionDelete="permissionDelete"
                        :estabelecimento="item.estabelecimento"
                        :index="countId++" 
                        :partnumbers="item.part_numbers"
                        :item="perguntas[index]"
                        :resposta="typeof resposta[item.id] === 'undefined' || resposta[item.id] == null ? '' : resposta[item.id]"
                        :label="item.pergunta"
                        :tooltips="tooltips"
                        :base-url="baseUrl"
                        @storeFiles="storeFiles"
                        @removeFiles="removeFiles"
                    />
            </div>
        </div>

        <hr />

        <button @click="submit" :disabled="disableButton" class="btn btn-primary" value="1" name="submit"><i class="glyphicon glyphicon-floppy-disk"></i> Salvar</button>
        <a :href="baseUrl + 'controle_pendencias'" class="btn">Cancelar</a>
    </div>
</template>
<script>
import 'vue-loading-overlay/dist/vue-loading.css';
import Loading from 'vue-loading-overlay';
import _ from 'lodash';
import vSelect from 'vue-select';
import Item from './Item.vue';

export default {
    data() {
        return {
            selectedPartnumbers: [],
            partnumbers: [],
            perguntas: [],
            attachments: [],
            isLoading: false,
            fullPage: true,
            disableButton: true,
            permissionDelete: 0,
            countId: 1,
            itensMarcados: 0,
            partnumberRecebido: ''
        }
    },
    props: {
        haspartnumbers: {
            required: false,
            type: Array
        },
        estabelecimento: {
            required: false,
            type: Array
        },
        baseUrl: {
            required: false,
            type: String
        },
        resposta: {
            required: true,
            type: String
        },
        tooltips: {
            required: false,
            type: Array
        }
    },
    methods: {
        getRespostas() {
            let partnumber = this.haspartnumbers;
            let estabelecimento = this.estabelecimento;

            this.$http.get('pr/respostas/getRespostas', {
                params: {
                    partnumber,
                    estabelecimento
                }
            }).then((response) => {
                if (response)
                {
                    this.resposta = response.data;
                } else {
                    this.resposta  = null;
                }
            });
        },
        removeFiles(ids) {
            this.attachments = this.attachments.filter((item) => {
                return item.ids != ids
            });
        },
        storeFiles(data) {
            this.attachments = this.attachments.filter((item) => {
                return item.ids != data.ids; 
            });

            this.attachments.push(data);
        },
        async handleSelect() {
            this.disableButton = true;
    
            const { data } = await this.getPerguntas();

            this.disableButton = false;

            this.perguntas = data.data.map((item) => {
                return item;
            })
        },
        getPermissao() {

            return this.$http.get('pr/perguntas/getPermissao');
        },
        getPerguntas() {
            let partnumbers = {
                    item: this.partnumberRecebido,
                    estabelecimento: this.estabelecimento
                };

            return this.$http.get('pr/perguntas/getPerguntasPendentesAgrupadasPn', {
                params: {
                    partnumbers
                }
            });
        },
        getPartnumbers() {
            
            return this.$http.get('pr/respostas/getPartnumbersSemRespostas', {
                params: {
                    partnumbers : this.partnumberRecebido,
                    estabelecimento: this.estabelecimento
                }
            });

        },
        getOptions() {
            let xx = [];
                  for (let i=0; i<this.partnumbers.length; i++) {        
                     xx[i] = this.partnumbers[i].key
                };

            return xx;
        },
        async requestPartnumbers() {
            this.disableButton = true;

            const { data } = await this.getPartnumbers();

            this.disableButton = false;

            this.partnumbers = data.data.map((item) => {
                return {
                    key: item.part_number,
                    label: item.part_number,
                    item: item,
                    estabelecimento: item.estabelecimento
                };
            })
        },
        answerQuestions() {
            this.isLoading = true;
            var self = this;
            let form = new FormData();

            for (let i=0; i<this.attachments.length; i++) {
                form.append('arquivos[]',this.attachments[i]);
            }

            this.attachments.forEach((item) => {
                for (let i=0; i<item.files.length; i++) {
                    form.append('arquivos[]',item.files[i]);
                }

                form.append('infoArquivos[]', JSON.stringify({
                    ids: item.ids,
                    quantidade: item.qtdFiles,
                    nomeArquivos: item.fileNames
                }));
            });

            var perguntas = [];

            this.perguntas.forEach((pergunta) => {
                // pergunta['pergunta'] = pergunta['pergunta'].replace(/[\u00A0-\u9999<>\&]/gim, function (i) {
                //     return '&#' + i.charCodeAt(0) + ';';
                // });


                //var textArea = document.createElement('textarea');
                //textArea.innerText = pergunta['pergunta']; // return textArea.innerHTML;
                // pergunta['pergunta'] = textArea.innerHTML;


                //pergunta['pergunta'] = encodeURIComponent(pergunta['pergunta']);


                pergunta['pergunta'] = pergunta['pergunta'].replace(/./gm, function (s) {
                    return (s.match(/[a-z0-9\s]+/i)) ? s : "&#" + s.charCodeAt(0) + ";";
                });

                perguntas.push(pergunta)
            });



            form.append("perguntas", JSON.stringify(perguntas));
            
            const config = { headers: { 'Content-Type': 'multipart/form-data' } };

            this.disableButton = true;

            this.$http.post("pr/respostas/responderPerguntas", form, config).then((data) => {
                if (data.data.error) {
                    swal("Oops!", data.data.msg, "error");
                } else {
                    swal("Sucesso!", "As perguntas foram respondidas.", "success");

                    this.attachments = [];

                    setTimeout(() => {
                        location.href = this.baseUrl + 'controle_pendencias';
                    }, 500);
                }
                
            }).catch((error) => {
                if (error.response && error.response.status === 400 && error.response.data && error.response.data.msg) {
                    swal("Erro!", error.response.data.msg, "error");
                } else {
                    swal("Erro!", "Ocorreu um erro na requisição.", "error");
                }
            
            });
 
        },
        submit() {
            let hasUnansweredQuestions = null;
            let unansweredMessage = null;
            let campoObrigatorio = false;
            var self = this;

            for (let i = 0; i < this.perguntas.length; i++) {
                const item = this.perguntas[i];

                if (_.isEmpty(item.resposta) && !_.isEmpty( this.resposta[item.id]))
                {
                    item.resposta = this.resposta[item.id];
                }

                if (_.isEmpty(item.resposta) && item.obrigatorio == "1") {
                     swal({
                        title: "Atenção!",
                        text: "Esta é uma pergunta obrigatória para responder <br><strong>" + item.pergunta + "</strong>",
                        type: "warning",
                        confirmButtonText: "OK",
                        showConfirmButton: true,
                        showCancelButton: false,
                    });

                    campoObrigatorio = true;

                    break;
                }

               // if (_.isEmpty(item.resposta) && !item.hasOwnProperty('resposta')) {
                 if (item.pendente == 1) { 
                    hasUnansweredQuestions++;
                }
            };

            if (campoObrigatorio) {
                return false;
            }

            unansweredMessage = hasUnansweredQuestions == 1 ? "pergunta" : "perguntas"; 

            if (hasUnansweredQuestions) {
                swal({
                    title: 'Atenção',
                    text: `Você deixou de responder a ${hasUnansweredQuestions} ${unansweredMessage}. Deseja continuar?`,
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Continuar',
                    cancelButtonText: 'Cancelar',
                    confirmButtonClass: 'btn btn-primary btn-margin-left',
                    cancelButtonClass: 'btn',
                    reverseButtons: true,
                    buttonsStyling: false
                }).then(function() {
                    self.isLoading = true;
                    self.answerQuestions();
                }, function(dismiss) {
                    //do nothing
                });
            } else {
                self.isLoading = true;
                self.answerQuestions();
            }
        }
    },
    async mounted() {
       
        this.isLoading = true;
        this.disableButton = true;
        this.permissionDelete = await  this.getPermissao();
        this.partnumberRecebido = this.haspartnumbers;
        await this.requestPartnumbers();
           
            let itensValue = this.haspartnumbers.map((item) => {
                return item.split('&')[0];
            })

            this.haspartnumbers = itensValue.filter(function(este, i) {
                return itensValue.indexOf(este) === i;
            }); 

        this.disableButton = false;
        this.getRespostas();
        if (!_.isEmpty(this.haspartnumbers)) {
            this.selectedPartnumbers = this.partnumbers.filter((item) => {
                let partnumberIncluded = this.haspartnumbers.filter(pn => pn == item.key);
                return !_.isEmpty(partnumberIncluded);                
            });

            this.handleSelect();
        }
        this.isLoading = false;
    },
    components: {
        vSelect, Item, Loading
    }
}
</script>

<style scoped>
    @import "vue-select/src/scss/vue-select.scss";
    .mt-5 {
        margin-top: 15px;
    }
</style>