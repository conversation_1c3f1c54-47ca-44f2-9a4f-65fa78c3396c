<?php $separador = get_company_separator(sess_user_company()); ?>

<?php 
    if($this->input->is_set('part_number')){ 
        $partnumbers = implode($separador, $this->input->get('part_number'));
    }else{ 
        $partnumbers = '';
    }
    ?>

<script type="text/javascript" src="<?php echo base_url('assets/js/atribuir_grupos/desvincular_grupos.js'); ?>"></script>
<script type="text/javascript">
    $(function() {

        desvincular_grupos.init('<?php echo site_url(); ?>');

        $('#send_search_itens').click(function(e) {
            e.preventDefault();
            desvincular_grupos.get_cad_itens();
        });

        // Verifica se os atributos part_number foram enviados via GET
        // var partNumbers = "<?php //echo implode(' & ', $this->input->get('part_number')); ?>";
        var partNumbers = "<?php echo $partnumbers ?>";

        if (partNumbers != '') {
            $('#item_input').val(partNumbers);
            console.log($('#item_input').val())
            desvincular_grupos.get_cad_itens();

            // Limpa o input depois de buscar os itens
            // $('#item_input').val('');
        }

        $(document).on('click', '#grupo_selected', function(e) {
            desvincular_grupos.selected_group(this);
            desvincular_grupos.get_cad_itens();
        });

        $('#send_search_grupos_tarifarios').click(function(e) {
            e.preventDefault();
            desvincular_grupos.get_grupos();
        });

        $(document).on('click', '#ncm_info', function(e) {
            desvincular_grupos.show_ncm_detail(this);
        });

        $(document).on('click', '.show-obs', function(e) {
            desvincular_grupos.show_obs_detail(this);
        });

        $('#delete_association').click(function(e) {
            e.preventDefault();
            desvincular_grupos.save();
        });

        $('#item_order_pnumber').click(function() {
            desvincular_grupos.change_order('#order_item', 'c.part_number');
            desvincular_grupos.get_cad_itens();
        });

        $('#item_order_descricao').click(function() {
            desvincular_grupos.change_order('#order_item', 'i.descricao');
            desvincular_grupos.get_cad_itens();
        });

        $('#grupo_order_descricao').click(function() {
            desvincular_grupos.change_order('#order_grupo', 'descricao');
            desvincular_grupos.get_grupos();
        });

        $('#grupo_order_ncm_recomendada').click(function() {
            desvincular_grupos.change_order('#order_grupo', 'ncm_recomendada');
            desvincular_grupos.get_grupos();
        });

        $('#select-all').on('change', function(e) {
            desvincular_grupos.select_all('#table-itens', 'item', e);
        });
    });
</script>
<div id="ajax_validate"></div>
<div class="page-header">
    <h2>Desfazer atribuição <small>Indique os itens que deseja desvincular</small>
        <a href="<?php echo site_url('atribuir_grupo'); ?>" class="btn btn-default pull-right">
            <i class="glyphicon glyphicon-arrow-left"></i> Voltar
        </a>
        <div class="col-md-3 pull-right">
            <button class="btn btn-danger btn-block" data-toggle="modal" data-target="#modal_motivo">
                <i class="glyphicon glyphicon-remove"></i> Desfazer atribuição
            </button>
        </div>
    </h2>
</div>
<div class="row">

    <div class="col-md-12">
        <div id="search_itens_holder">
            <form id="search_itens" method="POST" action="<?php echo site_url('atribuir_grupo/ajax_get_cad_itens'); ?>">
                <input type="hidden" id="order_item" name="descricao" value="asc" />
                <div class="input-group col-md-6">
                    <input type="text" class="form-control" name="item_input" id="item_input" placeholder="Digite o(s) código(s) ou descrição do(s) item(ns)" />
                    <div class="input-group-btn">
                        <button type="submit" data-loading-text="..." class="btn btn-primary" id="send_search_itens"><i class="glyphicon glyphicon-search"></i></button>
                    </div>
                </div>
            </form>
        </div>
        <div class="clearfix" style="margin-top: 10px;"></div>
        <table class="table table-striped" id="table-itens" data-multi-estabelecimentos="<?php echo $multi_estabelecimentos; ?>">
            <thead>
                <th width="1%">
                    <input type="checkbox" id="select-all" />
                </th>
                <th width="15%"><a href="javascript:void(0);" id="item_order_pnumber">Part Number</a></th>
                <?php if ($multi_estabelecimentos == 1) { ?>
                    <th width="15%" class="text-center">Estab.</th>
                <?php } ?>
                <th width="15%"><a href="javascript:void(0);" id="item_order_descricao">Descrição</a></th>
                <th width="20%">Grupo tarifário</th>
                <th width="10%">NCM Recomendada</th>
                <th width="20%">Observações</th>
            </thead>
            <tbody id="itens_holder"></tbody>
        </table>
        <div id="no_data"></div>
    </div>
</div>
<div class="clearfix" style="margin-top: 20px;"></div>
<div class="modal fade" id="modal_motivo" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" style="width: 50%">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Desvincular grupo</h4>
            </div>
            <div class="modal-body">
                <div id="validate_motivo"></div>
                <div class="form-group">
                    <label class="control-label pull-left" style="line-height: 29px;">Observações*: </label>
                    <textarea class="form-control" id="observacoes" rows="3" name="observacoes"><?php echo set_value('observacoes'); ?></textarea>
                    <small class="pull-left" id="countdown_obs"></small>
                    <small class="pull-right"><i>* Informação de preenchimento obrigatório.</i></small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Fechar</button>
                <button class="btn btn-primary" data-loading-text="Carregando..." id="delete_association">
                    Salvar
                </button>
            </div>
        </div>
    </div>
</div>