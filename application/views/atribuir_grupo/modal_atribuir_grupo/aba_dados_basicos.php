<div role="tabpanel" class="tab-pane active" id="dados" style="padding-top: 10px">
    <?php if ($user->id_empresa != 1 && !has_role('sysadmin')) : ?>
        <div class="alert alert-info alert-multiple-selection">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <h4>Atenção</h4>
            Não é possível alterar as informações após salvar a inclusão, somente entrando em contato com os consultores na Becomex.
        </div>
    <?php endif; ?>
    <div class="alert alert-warning alert-multiple-selection hide">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
        <h4>Atenção</h4>
        Você selecionou vários itens para atribuição, se você deseja herdar as informações de Marca, Função, Aplicação e Material Constitutivo, deixe os campos sem preenchimento.
    </div>
    <div id="validate_motivo"></div>
    <div class="form-group text-right">

        <?php if (in_array('preencher_desc_resumida', $funcoes_adicionais)): ?>
            <button class="btn btn-primary" id="btn_preencher_desc_resumida">
                Preencher Descrição Proposta Resumida
            </button>
            <input type="hidden" id="preencher_desc_resumida" name="preencher_desc_resumida" value="0">
        <?php endif; ?>
        <?php if ($empresa_pais) : ?>
            <button id="modal-multi-paises-diana" class="btn btn-primary pull-right hide desbloqueia_item" type="button" style="margin-left: 4px;">
                Salvar e Configurar Multi Países
            </button>
        <?php endif; ?>
        <?php if (has_role('sysadmin') || has_role('consultor')) : ?>
            <?php $this->load->view('atribuir_grupo/modal_atribuir_grupo/concatenar-campos') ?>
        <?php endif; ?>
    </div>
    <div class="form-group">
        <?php if (in_array('descricao_proposta_completa', $campos_adicionais)) : ?>
            <div class="form-group">
                <label for="" class="control-label pull-left" style="line-height: 29px">Descrição proposta completa: </label>
                <textarea maxlength="5000" type="text" class="form-control" name="descricao_proposta_completa" id="descricao_proposta_completa" placeholder="Descrição proposta completa" value="<?php echo set_value('descricao_proposta_completa'); ?>"></textarea>
            </div>
        <?php endif; ?>

        <?php if (in_array('observacoes', $campos_adicionais)) : ?>
            <div class="form-group">
                <label for="" class="control-label pull-left" style="line-height: 29px">Observações (Mestre de Itens): </label>
                <textarea type="text" class="form-control" name="observacoes_mestre" id="observacoes_mestre" placeholder="Observações" value="<?php echo set_value('observacoes'); ?>"></textarea>
            </div>
        <?php endif; ?>

        <?php if (in_array('subsidio', $campos_adicionais)) { ?>
            <div class="form-group">
                <label class="control-label pull-left" style="line-height: 29px;">Subsídio: </label>
                <textarea maxlength="3000" class="form-control" id="subsidio" placeholder="Subsídio" rows="2" name="subsidio"><?php echo set_value('subsidio'); ?></textarea>
            </div>
        <?php } ?>
        <?php if (in_array('descricao_mercado_local', $campos_adicionais)) : ?>
            <div class="form-group clearfix">
                <label for="" class="control-label pull-left" style="line-height: 29px">Descrição proposta resumida: </label>
                <textarea maxlength="5000" type="text" class="form-control" name="descricao_mercado_local" id="descricao_mercado_local" placeholder="Descrição proposta resumida" value="<?php echo set_value('descricao_mercado_local'); ?>"></textarea>
                <small class="pull-left" id="count_proposta_resumida"></small>
            </div>
        <?php endif; ?>
        <?php if (in_array('descricao', $campos_adicionais)) : ?>
            <div class="form-group">
                <label for="descricao" class="control-label pull-left" style="line-height: 29px">Descrição: </label>
                <textarea maxlength="5000" type="text" class="form-control" name="descricao" id="descricao" placeholder="Descrição" value="<?php echo set_value('descricao'); ?>"></textarea>
            </div>
        <?php endif; ?>
        <?php if (in_array('evento', $campos_adicionais)) : ?>
            <div class="form-group">
                <label for="" class="control-label pull-left" style="line-height: 29px">Evento: </label>
                <textarea maxlength="5000" type="text" class="form-control" name="evento" id="evento" placeholder="Evento" value="<?php echo set_value('evento'); ?>"></textarea>
            </div>
        <?php endif; ?>
        <?php if (in_array('marca', $campos_adicionais)) { ?>
            <div class="form-group">
                <label class="control-label pull-left" style="line-height: 29px;">Marca: </label>
                <input maxlength="3000" class="form-control" name="marca" id="marca" placeholder="Marca" value="<?php echo set_value('marca'); ?>">
            </div>
        <?php } ?>
        <?php if (in_array('funcao', $campos_adicionais)) { ?>
            <div class="form-group">
                <label class="control-label pull-left" style="line-height: 29px;">Função: </label>
                <textarea maxlength="3000" class="form-control" id="funcao" placeholder="Função" rows="3" name="funcao"><?php echo set_value('funcao'); ?></textarea>
            </div>
        <?php } ?>
        <?php if (in_array('aplicacao', $campos_adicionais)) { ?>
            <div class="form-group">
                <label class="control-label pull-left" style="line-height: 29px;">Aplicação: </label>
                <textarea maxlength="3000" class="form-control" id="aplicacao" placeholder="Aplicação" rows="3" name="aplicacao"><?php echo set_value('aplicacao'); ?></textarea>
            </div>
        <?php } ?>
        <?php if (in_array('inf_adicionais', $campos_adicionais)) { ?>
            <div class="form-group">
                <label class="control-label pull-left" style="line-height: 29px;">Informações Adicionais: </label>
                <textarea maxlength="3000" class="form-control" id="inf_adicionais" placeholder="Informações Adicionais" rows="3" name="inf_adicionais"><?php echo set_value('inf_adicionais'); ?></textarea>
            </div>
        <?php } ?>
        <?php if (in_array('material_constitutivo', $campos_adicionais)) { ?>
            <div class="form-group">
                <label class="control-label pull-left" style="line-height: 29px;">Material Constitutivo: </label>
                <textarea maxlength="3000" class="form-control" id="material_constitutivo" placeholder="Material Constitutivo" rows="2" name="material_constitutivo"><?php echo set_value('material_constitutivo'); ?></textarea>
            </div>
        <?php } ?>
        <?php if (in_array('origem', $campos_adicionais)) { ?>
            <div class="form-group">
                <label class="control-label pull-left" style="line-height: 29px;">Origem (País): </label>
                <textarea maxlength="3000" class="form-control" id="origem" placeholder="Origem (País)" rows="3" name="origem"><?php echo set_value('origem'); ?></textarea>
            </div>
        <?php } ?>
        <?php if (in_array('maquina', $campos_adicionais)) { ?>
            <div class="form-group">
                <label class="control-label pull-left" style="line-height: 29px;">Máquina: </label>
                <textarea maxlength="3000" class="form-control" id="maquina" placeholder="Máquina" rows="3" name="maquina"><?php echo set_value('maquina'); ?></textarea>
            </div>
        <?php } ?>
        <?php if (in_array('caracteristica', $campos_adicionais)) { ?>
            <div class="form-group">
                <label class="control-label pull-left" style="line-height: 29px;">Característica: </label>
                <textarea maxlength="3000" class="form-control" id="caracteristica" placeholder="Característica" rows="2" name="caracteristica"><?php echo set_value('caracteristica'); ?></textarea>
            </div>
        <?php } ?>
        <?php if (in_array('memoria_classificacao', $campos_adicionais)) { ?>
            <div class="form-group">
                <label class="control-label pull-left" style="line-height: 29px;">Memória de Classificação: </label>
                <textarea maxlength="3000" class="form-control" id="memoria_classificacao" placeholder="Memória de Classificação" rows="2" name="memoria_classificacao"><?php echo set_value('memoria_classificacao'); ?></textarea>
            </div>
        <?php } ?>
        <div class="form-group">
            <label class="control-label pull-left" style="line-height: 29px;">Observações: </label>
            <textarea maxlength="3000" class="form-control" id="observacoes" placeholder="Observações" rows="3" name="observacoes"><?php echo set_value('observacoes'); ?></textarea>
            <small class="pull-left" id="countdown_obs"></small>
        </div>

        <?php if (in_array('controle_drawback', $campos_adicionais)) { ?>
            <br />
            <div class="form-group">
                <label>
                    <input type="checkbox" id="is_drawback" name="is_drawback" value="1"> Controle de Drawback
                </label>
            </div>
        <?php } ?>
    </div>
</div>