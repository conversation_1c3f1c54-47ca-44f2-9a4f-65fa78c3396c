<div class="row">
    <div class="col-sm-12">
        <table class="table table-responsive">
            <thead>
                <tr>
                    <th>Estabelecimento</th>
                    <th>Pergunta</th>
                    <th>Data pergunta</th>
                    <th>Resposta</th>
                    <th>Data resposta</th>
                    <th>Responsável</th>
                    <th>Respondido por</th>
                    <th>Arquivos</th>
                </tr>
            </thead>
            <tbody>
                <?php  foreach ($perguntas as $pergunta) : ?>
                    <tr>
                        <td><?php echo !empty($pergunta->estabelecimento) ? $pergunta->estabelecimento : 'N/A' ?></td>
                        <td><?php echo $pergunta->pergunta ?></td>
                        <td><?php echo date('d/m/Y H:i:s', strtotime($pergunta->criado_em)); ?></td>
                        <td>
                            <?php if ($pergunta->pendente == 1) : ?>
                                <?php echo '-'; ?>
                            <?php else : ?>
                                <?php echo !empty($pergunta->resposta) ? $pergunta->resposta : 'Sem resposta' ?>
                            <?php endif; ?>
                        </td>
                        <td><?php echo !empty($pergunta->resposta) ? date('d/m/Y H:i:s', strtotime($pergunta->respondida_em)) : '-' ?></td>
                        <td><?php echo $pergunta->nome ? $pergunta->nome : $pergunta->cod_owner . ' - ' . $pergunta->owner ?></td>
                        <td><?php echo $pergunta->usu_resposta ?></td>
                        <td>
                            <?php if (!empty($pergunta->arquivo)) : ?>
                                <div class="dropdown" style="display: inline" >
                                    <button class="btn btn-secondary dropdown-toggle" style="margin-bottom: 5px" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="glyphicon glyphicon-download"></i>
                                    </button>
                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                        <?php foreach ($pergunta->arquivo as $arquivo) : ?>
                                            <a class="dropdown-item btn-download" title="<?php echo $arquivo->nome ?>" href="<?php echo base_url() ?>assets/respostas/<?php echo $arquivo->nome ?>" download>
                                                <?php echo $arquivo->nome ?>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </td>

                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>

<style scoped>
.form-group.files .form-control {
  padding: 5px 4px;
}

.btn-download {
  display: block;
  padding: 5px 12px;
  border-bottom: 1px solid rgb(211, 211, 211);
}

.btn-download:last-child {
  display: block;
  padding: 5px 12px;
  border-bottom: none;
}

.form-control .input-file {
  padding: 5px 5px !important;
}

td {
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>