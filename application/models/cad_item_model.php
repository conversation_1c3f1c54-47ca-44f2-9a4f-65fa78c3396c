<?php

class Cad_item_model extends MY_Model
{

    public $_virtual_table = [];
    public $_attr_default_item = [];
    public $_attrs = [];
    public $_table = 'cad_item';
    public $_userId = '';
    private $recentEntries = [];
    public $_attr_estrutura_completa = [];

    public function __construct()
    {
        parent::__construct();
    }

    public function get_descricao_resumida($id_item)
    {
        $this->db->select(
            '
            gt.tipo_descricao_resumida,
            gt.descricao as grupo_descricao,
            ci.descricao_mercado_local'
        );

        $this->db->where('ci.id_item', $id_item);
        $this->db->join('grupo_tarifario gt', 'ci.id_grupo_tarifario = gt.id_grupo_tarifario', 'left');

        $query = $this->db->get($this->_table . ' ci');

        if ($query->num_rows() == 0) {
            return;
        }

        $item = $query->row();

        return $item->descricao_mercado_local;
    }

    public function get_similar_pn($part_number)
    {
        $this->db->where('i.id_empresa', sess_user_company());

        $this->db->select('i.part_number_similar');
        $this->db->like('ci.part_number', $part_number, 'none');

        $this->db->join('item i', 'i.part_number = ci.part_number AND i.id_empresa = ci.id_empresa AND i.estabelecimento = ci.estabelecimento', 'inner');
        $query = $this->db->get($this->_table . ' ci');
        if ($query->num_rows() > 0) {
            $result = $query->row();
            return $result->part_number_similar;
        }

        return FALSE;
    }

    public function get_entry_by_search($search, $id_empresa)
    {
        $this->db->select('ci.*, gt.*, i.descricao as descricao_item');
        $this->db->where('ci.id_empresa', $id_empresa);
        $this->db->where("(i.descricao LIKE '%" . $search . "%' OR ci.part_number LIKE '%" . $search . "%')", NULL, NULL);

        $this->db->join('grupo_tarifario gt', 'ci.id_grupo_tarifario = gt.id_grupo_tarifario', 'left');
        $this->db->join('item i', 'i.part_number = ci.part_number AND i.id_empresa = ci.id_empresa', 'left');

        $query = $this->db->get($this->_table . ' ci');

        return $query->result();
    }

    public function get_entry_by_pn($part_number, $id_empresa, $estabelecimento = NULL, $desativar = false)
    {

        $this->db->where('i.id_empresa', $id_empresa);
        $this->db->where('part_number', $part_number);

        if (!empty($estabelecimento)) {
            $this->db->where('i.estabelecimento', $estabelecimento);
        }

        $query = $this->db->get($this->_table . ' i');
        if ($query->num_rows() > 0) {
            return $query->row();
        } else if ($desativar == false) {
            return [];
        }
    }

    private function get_query_entries_by_or_desc($search, $id_empresa, $grupo = NULL, $get_inactive = false)
    {
        $join_arr[]  = "INNER JOIN item i ON i.part_number = c.part_number AND i.id_empresa = c.id_empresa AND i.estabelecimento = c.estabelecimento";
        $join_arr[]  = "INNER JOIN grupo_tarifario g ON g.id_grupo_tarifario = c.id_grupo_tarifario";
        $join_arr[]  = "LEFT JOIN empresa_prioridades ep ON ep.id_prioridade = i.id_prioridade";

        if ($get_inactive == true) {
            $where_arr[] = "i.id_empresa = {$id_empresa} AND i.id_status IN ('1','2','3','4','5','6','7','8','9','10','11','12','13')";
        } else {
            $where_arr[] = "i.id_empresa = {$id_empresa}";
        }



        if ($has_origem = $this->get_state('filter.has_origem')) {
            $where_arr[] = "c.origem = 'atribuicao_manual'";
        }


        if ($prioridades = $this->get_state("filter.prioridade")) {
            if (count($prioridades) > 1) {
                $where_str = "(";
                foreach ($prioridades  as $key => $prioridade) {
                    if ($key == 0) {
                        $where_str .=  "i.id_prioridade = {$prioridade}";
                    } else {
                        $where_str .=  "OR i.id_prioridade = {$prioridade}";
                    }
                }
                $where_str = ")";
                $where_arr[] = $where_str;
            } else {
                $where_arr[] = "i.id_prioridade = {$prioridades} ";
            }
        }

        if (!empty($grupo)) {
            $where_arr[] = "c.id_grupo_tarifario = {$grupo}";
        }

        if ($this->get_state('filter.homologados') && $this->get_state('filter.reprovados')) {
            $where_arr[] = "(EXISTS (SELECT 1 FROM cad_item_homologacao cih WHERE cih.id_item = c.id_item AND homologado = 0)
            AND NOT EXISTS (SELECT 1 FROM cad_item_homologacao cih WHERE cih.id_item = c.id_item AND homologado = 2) OR 
            EXISTS (SELECT 1 FROM cad_item_homologacao cih WHERE cih.id_item = c.id_item AND homologado = 1) OR
            NOT EXISTS (SELECT 1 FROM cad_item_homologacao cih where cih.id_item = c.id_item))";
        } else if ($reprovados = $this->get_state('filter.reprovados')) {
            $where_arr[] = "EXISTS (SELECT 1 FROM cad_item_homologacao cih WHERE cih.id_item = c.id_item AND homologado = 0)
             AND NOT EXISTS (SELECT 1 FROM cad_item_homologacao cih WHERE cih.id_item = c.id_item AND homologado = 2)";
        }

        $wildcards_search = $this->apply_search_rules(TRUE, 'i.part_number', 'i.descricao');

        if (!empty($wildcards_search)) {
            $where_arr[] = $wildcards_search;
        }

        $select = "i.*,
            c.id_grupo_tarifario,
            g.descricao as nome_grupo,
            g.ncm_recomendada,
            g.observacao,
            ep.nome as empresa_prioridade";

        $from  = "cad_item c";
        $join  = implode(' ', $join_arr);
        $where = implode(' AND ', $where_arr);
        $limit = 500;

        $query = "SELECT {$select} FROM {$from} {$join} WHERE {$where}";

        return $query;
    }

    public function get_entries_by_pn_or_desc($search, $id_empresa, $grupo = NULL, $order_by = NULL)
    {
        $limit = 500;

        $this->set_state('filter.has_origem', TRUE);
        $query_itens_atribuidos = $this->get_query_entries_by_or_desc($search, $id_empresa, $grupo, TRUE);

        $this->set_state('filter.has_origem', FALSE);
        $this->set_state('filter.reprovados', TRUE);
        $query_itens_reprovados = $this->get_query_entries_by_or_desc($search, $id_empresa, $grupo, FALSE);

        $final_query = "{$query_itens_atribuidos} UNION {$query_itens_reprovados}  GROUP BY c.id_item ORDER BY {$order_by} LIMIT {$limit}";
        $final_query = $this->db->query($final_query);

        return $final_query->result();
    }

    private function apply_search_rules(
        $return_query = false,
        $col_part_number = 'c.part_number',
        $col_descricao = 'c.descricao_mercado_local'
    ) {
        $part_numbers = $this->get_state('filter.part_numbers');
        $adwhere = array();

        $separador_pesquisa = get_company_separator(sess_user_company());

        // Se for uma lista de part_numbers válidos
        if ($this->is_valid_partnumber_list($part_numbers)) {
            // Separa os part_numbers em um array usando o separador da empresa
            if (!empty($separador_pesquisa)) {
                $terms = is_array($part_numbers) ?
                    (count($part_numbers) == 1 && is_string($part_numbers[0])) ?
                    explode($separador_pesquisa, $part_numbers[0]) : $part_numbers
                    : explode($separador_pesquisa, $part_numbers);
            } else {
                // Se não há separador definido, trata como um único part_number
                $terms = is_array($part_numbers) ? $part_numbers : [$part_numbers];
            }

            // Filtrar elementos vazios que podem surgir de separadores múltiplos consecutivos
            $terms = array_filter($terms, function ($term) {
                return trim($term) !== '';
            });

            // Escapa cada termo para evitar SQL Injection
            $escaped_terms = array_map(function ($term) {
                return $this->db->escape(trim($term));
            }, $terms);

            // Monta a condição IN para part_numbers
            $adwhere[] = "{$col_part_number} IN (" . implode(',', $escaped_terms) . ")";
        } else {
            // Busca por descrição (LIKE com o termo completo)
            $search_term = is_array($part_numbers) ? implode(' ', $part_numbers) : $part_numbers;
            $escaped_term = $this->db->escape_like_str($search_term);
            $adwhere[] = "{$col_descricao} LIKE '%{$escaped_term}%'";
        }

        // Combina as condições com OR
        $query_where = !empty($adwhere) ? '(' . implode(' OR ', $adwhere) . ')' : null;

        if ($return_query) return $query_where;
        if ($query_where) $this->db->where($query_where, NULL, FALSE);
    }

    // Função auxiliar para validar se todos os termos são part_numbers
    private function is_valid_partnumber_list($input)
    {
        if (empty($input)) {
            return false;
        }

        $separador_pesquisa = get_company_separator(sess_user_company());

        // Separa usando o separador da empresa ou trata como único item
        if (!empty($separador_pesquisa)) {
            $terms = is_array($input) ?
                ((count($input) == 1 && is_string($input[0])) ? explode($separador_pesquisa, $input[0]) : $input)
                : explode($separador_pesquisa, $input);
        } else {
            // Se não há separador definido, trata como um único part_number
            $terms = is_array($input) ? $input : [$input];
        }

        // Filtrar elementos vazios que podem surgir de separadores múltiplos consecutivos
        $terms = array_filter($terms, function ($term) {
            return trim($term) !== '';
        });

        // Regex: Permite letras, números, hifen, underline, pontos e espaços (para part_numbers com espaços internos)
        $pattern = '/^[a-zA-Z0-9-_\.\s]+$/';

        foreach ($terms as $term) {
            $trimmed_term = trim($term);
            if (empty($trimmed_term) || !preg_match($pattern, $trimmed_term)) {
                return false; // Termo inválido como part_number
            }
        }
        return true; // Todos são part_numbers válidos
    }

    public function get_all_evento_in_item_by_empresa()
    {
        try {
            $id_empresa = sess_user_company();
            $this->db->select('DISTINCT i.evento', false);
            $this->db->where('i.id_empresa', $id_empresa);
            $this->db->where('i.evento IS NOT NULL', false, false);
            $this->db->where("i.evento <> ''", false, false);
            // $this->db->group_by('i.evento');
            $this->db->order_by('i.evento asc');

            $this->db->join('item i', 'i.part_number = ci.part_number AND i.id_empresa = ci.id_empresa', 'inner');

            $query = $this->db->get('cad_item ci');

            return $query->result();
        } catch (Exception $e) {
            throw new $e->getMessage();
        }
    }

    public function get_entries_by_pn_or_desc_mercado_local($atributos_obrigatorios = NULL)
    {
        $this->load->model("empresa_model");
        $funcoes_adicionais = $this->empresa_model->get_funcoes_adicionais(null, sess_user_company());

        $useViewComex = $this->config->item('use_view_comex');

        if ($useViewComex && $this->db->table_exists('vw_comex')) {
            $this->db->select('c.*, 
            c.descricao_mercado_local as descricao, 
            ni.bk, 
            ni.bit,
            vw_comex.ind_ecomex as indicador_ecomex,
            vw_comex.num_di as num_di,
            vw_comex.data_di as data_di,
            vw_comex.ind_drawback as ind_drawback,
            vw_comex.ncm as ncm_ecomex,
            ');
        } else if (!$useViewComex && $this->db->table_exists('comex')) {
            $this->db->select('c.*, 
            c.descricao_mercado_local as descricao, 
            ni.bk, 
            ni.bit,
            comex.ind_ecomex as indicador_ecomex,
            comex.num_di as num_di,
            comex.data_di as data_di,
            comex.ind_drawback as ind_drawback,
            comex.ncm as ncm_ecomex,
            ');
        } else {
            $this->db->select('c.*, 
            c.descricao_mercado_local as descricao, 
            ni.bk, 
            ni.bit,
            ');
        }

        // if ($useViewComex && $this->db->table_exists('vw_comex')) {
        // 	$this->db->join('vw_comex', 'vw_comex.part_number_original = c.part_number AND vw_comex.id_empresa = c.id_empresa', 'left');
        // } else if (!$useViewComex && $this->db->table_exists('comex')) {
        //     $this->db->join('comex FORCE INDEX (index_helper)', 'comex.part_number_original = c.part_number AND comex.id_empresa = c.id_empresa AND comex.unidade_negocio = c.estabelecimento', 'left');
        // }

        $adwhere = $this->apply_search_rules(true, 'c.part_number', 'c.descricao_mercado_local');

        if ($adwhere) {
            $this->db->where($adwhere, null, false);
        }

        if (in_array("integracao_ecomex", $funcoes_adicionais) && $useViewComex && $this->db->table_exists('vw_comex')) {
            $this->db->join('vw_comex', 'vw_comex.part_number_original = c.part_number
                AND vw_comex.id_empresa = c.id_empresa
                AND vw_comex.unidade_negocio = c.estabelecimento
                AND (vw_comex.ind_ecomex = "EN" OR vw_comex.ind_ecomex = "NE")', 'left');
        } elseif (in_array("integracao_ecomex", $funcoes_adicionais) && !$useViewComex && $this->db->table_exists('comex')) {
            $this->db->join('comex', 'comex.part_number_original = c.part_number
                AND comex.id_empresa = c.id_empresa
                AND comex.unidade_negocio = c.estabelecimento
                AND (comex.ind_ecomex = "EN" OR comex.ind_ecomex = "NE")', 'left');
        } else {
            $this->db->join('comex', 'comex.part_number_original = c.part_number
            AND comex.id_empresa = c.id_empresa
            AND comex.unidade_negocio = c.estabelecimento
            AND (comex.ind_ecomex = "EN" OR comex.ind_ecomex = "NE")', 'left');
        }

        if ($ncm = $this->get_state('filter.ncm_proposto')) {
            $this->db->like("ncm_proposto", $ncm, 'after');
        }

        if ($grupo = $this->get_state('filter.grupo')) {
            $this->db->where('c.id_grupo_tarifario', $grupo);
            $this->db->where('c.origem = "atribuicao_manual"');
        }

        if ($order_by = $this->get_state('filter.order_by')) {
            $this->db->order_by($order_by);
        }

        if ($id_empresa = $this->get_state('filter.id_empresa')) {
            $this->db->where('c.id_empresa', $id_empresa);
        }

        if ($eventos = $this->get_state('filter.eventos')) {
            $this->db->join('item i', 'i.part_number = c.part_number AND i.id_empresa = c.id_empresa', 'inner');
            $this->db->where_in('i.evento', $eventos);

            $item_joined = TRUE;
        }

        if ($this->get_state('filter.auto_filtro') == 1) {
            $this->db->join('empresa_ex_tarifario empex', 'empex.cod_ncm = c.ncm_proposto and c.id_empresa = empex.id_empresa', 'inner');
        }

        if (!empty($group_by)) {
            $this->db->group_by($group_by);
        }

        $bk = $this->get_state('filter.bk');
        $bit = $this->get_state('filter.bit');

        if ($bk && $bit) {
            $this->db->where("(ni.bk = 'S' OR ni.bit = 'S')");
        } else {
            if ($bk) {
                $this->db->where('ni.bk', $bk);
            }

            if ($bit) {
                $this->db->where('ni.bit', $bit);
            }
        }
        $nacional = $this->get_state('filter.nacional');
        $importado = $this->get_state('filter.importado');

        if ($nacional || $importado) {
            // verifica se o join já não foi feito na chamada dos eventos
            if (!$item_joined) {
                $this->db->join('item i', 'i.part_number = c.part_number AND i.id_empresa = c.id_empresa', 'inner');
            }

            // se ambos selecionados
            if ($useViewComex && $nacional && $importado) {
                $this->db->where("(vw_comex.ind_ecomex = 'EN' OR vw_comex.ind_ecomex = 'NE' OR vw_comex.ind_ecomex = 'EI')", NULL, FALSE);
            } else if (!$useViewComex && $nacional && $importado) {
                $this->db->where("(comex.ind_ecomex = 'EN' OR comex.ind_ecomex = 'NE' OR comex.ind_ecomex = 'EI')", NULL, FALSE);
            } else {
                // se apenas nacional selecionado
                if ($useViewComex && $nacional) {
                    $this->db->where("(vw_comex.ind_ecomex = 'EN' OR vw_comex.ind_ecomex = 'NE')", NULL, FALSE);
                } else if (!$useViewComex && $nacional) {
                    $this->db->where("(comex.ind_ecomex = 'EN' OR comex.ind_ecomex = 'NE')", NULL, FALSE);
                }

                // se apenas importado selecionado
                if ($useViewComex && $importado) {
                    $this->db->where("(vw_comex.ind_ecomex = 'EI')", NULL, FALSE);
                } else if (!$useViewComex && $importado) {
                    $this->db->where("(comex.ind_ecomex = 'EI')", NULL, FALSE);
                }
            }
        }

        $this->db->join('ncm_impostos ni', 'ni.cod_ncm = c.ncm_proposto', 'left');

        if ($this->get_state('filter.with_metrics')) {
            $select_total_attrs = "(SELECT COUNT(*) as total_atributos FROM ncm_atributo 
                WHERE ncm = c.ncm_proposto AND forma_preenchimento <> 'COMPOSTO' AND (codigo_pai IS NULL OR codigo_pai = ''))";

            $select_total_ii = "(select     sum(case when nullif(c.num_ex_ii, '') is null then 1 else 0 end) as pendencias_ex_ii
            from    cad_item c
                    inner join ncm_valido_ex_ii ex_ii
                        on c.ncm_proposto = ex_ii.cod_ncm
            where   c.id_empresa = '{$id_empresa}')";

            $select_total_nve = "(select     count(*) as total
            from    cad_item c
                    inner join ncm_valido_nve nve
                        on c.ncm_proposto = nve.cod_ncm
            where   c.id_empresa = '{$id_empresa}')";

            $select_total_atribuido = "(SELECT COUNT(*) FROM cad_item_attr attr2 WHERE (attr2.atributo_pai IS NULL OR attr2.atributo_pai = '') AND (attr2.id_item = c.id_item) AND (attr2.codigo <> '' AND attr2.codigo IS NOT NULL))";

            $this->db->select("{$select_total_attrs} as total_atributos", false);
            $this->db->select("{$select_total_atribuido} AS total_atribuido", false);
            $this->db->select("(($select_total_attrs + $select_total_ii + $select_total_nve) - {$select_total_atribuido}) as total_pendentes", false);

            $this->db->having('total_pendentes > 0');
        }

        $this->db->join('cad_item_attr attr', 'c.id_item = attr.id_item', 'left');

        $this->db->group_by('c.id_item');
        $this->db->from('cad_item c');

        $query = $this->db->get();

        $itens = $query->result();

        return $itens;
    }

    public function get_entry($id_item)
    {
        $useViewComex = $this->config->item('use_view_comex');

        if ($useViewComex && $this->db->table_exists('vw_comex')) {
            $this->db->select('
				i.id_item, i.part_number, i.id_empresa,
				i.estabelecimento, i.descricao_mercado_local, i.ncm_proposto,
				i.caracteristicas, i.subsidio, i.dispositivo_legal,
				i.funcao, i.inf_adicionais, i.aplicacao, i.marca, i.material_constitutivo,
				i.solucao_consulta, i.status_implementacao, i.motivo_implementacao,
				i.id_grupo_tarifario, i.id_resp_fiscal, i.id_resp_engenharia,
				i.status_homologacao, i.memoria_classificacao, i.houve_funcao_manual, i.houve_inf_adicionais_manual,
				i.houve_aplicacao_manual, i.houve_marca_manual, i.num_ex_ii,
				i.num_ex_ipi, i.cod_cest as cod_cest_proposto, i.status_simplus,
				i.suframa_destaque, item.observacoes,
				i.suframa_ppb,
				i.suframa_descricao,
				i.suframa_produto,
				i.suframa_codigo,
                i.li,
                i.li_orgao_anuente,
                i.li_destaque,
                i.antidumping,
				item.maquina,
				item.origem,
                item.id_prioridade,
				i.id_classificacao_energetica,
				item.descricao as descricao_atual, item.descricao_proposta_completa, item.peso, item.prioridade,
				item.descricao_global,
				item.houve_descricao_completa_manual, item.ncm as ncm_atual, item.ncm_fornecedor, item.cod_cest as cod_cest_atual,
				gt.descricao as grupo_tarifario_desc, gt.observacao,
                vis.status as v_item_status,
				item.lista_cliente, item.lista_becomex,
				item.id_lessin, item.regra_aplicada,
				item.id_status as status_atual,
				status.slug as status_slug,
				item.id_status,
                item.is_drawback,
				vw_comex.ind_ecomex as indicador_ecomex,
				vw_comex.num_di as num_di,
				vw_comex.data_di as data_di,
				vw_comex.ind_drawback as ind_drawback,
				vw_comex.ncm as ncm_ecomex,
                item.dat_criacao,
                item.data_modificacao,
                ep.nome as empresa_prioridade,
                item.wf_status_atributos,
			');
        } else if (!$useViewComex && $this->db->table_exists('comex')) {
            $this->db->select('
				i.id_item, i.part_number, i.id_empresa,
				i.estabelecimento, i.descricao_mercado_local, i.ncm_proposto,
				i.caracteristicas, i.subsidio, i.dispositivo_legal,
				i.funcao, i.inf_adicionais, i.aplicacao, i.marca, i.material_constitutivo,
				i.solucao_consulta, i.status_implementacao, i.motivo_implementacao,
				i.id_grupo_tarifario, i.id_resp_fiscal, i.id_resp_engenharia,
				i.status_homologacao, i.memoria_classificacao, i.houve_funcao_manual, i.houve_inf_adicionais_manual,
				i.houve_aplicacao_manual, i.houve_marca_manual, i.num_ex_ii,
				i.num_ex_ipi, i.cod_cest as cod_cest_proposto, i.status_simplus,
				i.suframa_destaque, item.observacoes,
				i.suframa_ppb,
				i.suframa_descricao,
				i.suframa_produto,
				i.suframa_codigo,
                i.li,
                i.li_orgao_anuente,
                i.li_destaque,
                i.antidumping,
				item.maquina,
				item.origem,
                item.id_prioridade,
				i.id_classificacao_energetica,
				item.descricao as descricao_atual, item.descricao_proposta_completa, item.peso, item.prioridade,
				item.descricao_global,
				item.houve_descricao_completa_manual, item.ncm as ncm_atual, item.ncm_fornecedor, item.cod_cest as cod_cest_atual,
				gt.descricao as grupo_tarifario_desc, gt.observacao,
                vis.status as v_item_status,
				item.lista_cliente, item.lista_becomex,
				item.id_lessin, item.regra_aplicada,
				item.id_status as status_atual,
				status.slug as status_slug,
				item.id_status,
                item.is_drawback,
                comex.ind_ecomex as indicador_ecomex,
				comex.num_di as num_di,
				comex.data_di as data_di,
       
				comex.ind_drawback as ind_drawback,
				comex.ncm as ncm_ecomex,
                comex.codigo_receita as codigo_receita,
                comex.versao_produto as versao_produto,
                item.dat_criacao,
                item.data_modificacao,
                ep.nome as empresa_prioridade,
                item.wf_status_atributos,
			');
        } else {
            $this->db->select('
				i.id_item, i.part_number, i.id_empresa,
				i.estabelecimento, i.descricao_mercado_local, i.ncm_proposto,
				i.caracteristicas, i.subsidio, i.dispositivo_legal,
				i.funcao, i.inf_adicionais, i.aplicacao, i.marca, i.material_constitutivo,
				i.solucao_consulta, i.status_implementacao, i.motivo_implementacao,
				i.id_grupo_tarifario, i.id_resp_fiscal, i.id_resp_engenharia,
				i.status_homologacao, i.memoria_classificacao, i.houve_funcao_manual, i.houve_inf_adicionais_manual,
				i.houve_aplicacao_manual, i.houve_marca_manual, i.num_ex_ii,
				i.num_ex_ipi, i.cod_cest as cod_cest_proposto, i.status_simplus,
				i.suframa_destaque, item.observacoes,
				i.suframa_ppb,
				i.suframa_descricao,
				i.suframa_produto,
				i.suframa_codigo,
                i.li,
                i.li_orgao_anuente,
                i.li_destaque,
                i.antidumping,
				item.maquina,
				item.origem,
                item.id_prioridade,
				i.id_classificacao_energetica,
				item.descricao as descricao_atual, item.descricao_proposta_completa, item.peso, item.prioridade,
				item.descricao_global,
				item.houve_descricao_completa_manual, item.ncm as ncm_atual, item.ncm_fornecedor, item.cod_cest as cod_cest_atual,
				gt.descricao as grupo_tarifario_desc, gt.observacao,
                vis.status as v_item_status,
				item.lista_cliente, item.lista_becomex,
				item.id_lessin, item.regra_aplicada,
				item.id_status as status_atual,
				status.slug as status_slug,
				item.id_status,
                item.is_drawback,
                item.dat_criacao,
                item.data_modificacao,
                ep.nome as empresa_prioridade,
                item.wf_status_atributos,
			');
        }

        // Somente acesso aos itens da empresa do usuário
        if ($this->get_state('filter.id_empresa')) {
            $this->db->where('i.id_empresa', $this->get_state('filter.id_empresa'));
        }

        $this->db->join('item', 'i.part_number = item.part_number AND i.id_empresa = item.id_empresa AND i.estabelecimento = item.estabelecimento', 'inner');
        $this->db->join('grupo_tarifario gt', 'gt.id_grupo_tarifario = i.id_grupo_tarifario', 'left');
        $this->db->join('status', 'status.id = item.id_status', 'left');
        $this->db->join('empresa_prioridades ep', 'ep.id_prioridade = item.id_prioridade', 'left');

        // #4433 - FUNÇÃO DE TRANSFERENCIA DE USUÁRIO
        // Precisamos que quando o Item ainda estiver em REVISÃO, não poderá aparecer na tela de HOMOLOGAÇÃO.
        // -Andrei
        $this->db->join('v_item_status vis', 'vis.id_empresa = i.id_empresa AND vis.estabelecimento = i.estabelecimento AND vis.part_number = i.part_number', 'inner');

        if ($useViewComex && $this->db->table_exists('vw_comex')) {
            $this->db->join('vw_comex', 'vw_comex.part_number_original = i.part_number AND vw_comex.id_empresa = i.id_empresa', 'left');
        } else if (!$useViewComex && $this->db->table_exists('comex')) {
            $this->db->join('comex FORCE INDEX (index_helper)', 'comex.part_number_original = i.part_number AND comex.id_empresa = i.id_empresa AND comex.unidade_negocio = i.estabelecimento', 'left');
        }

        $this->db->where('i.id_item', $id_item);
        $query = $this->db->get($this->_table . ' i');

        if ($query->num_rows() > 0) {
            return $query->row();
        } else {
            throw new Exception("Nenhum item encontrado");
        }
    }

    public function get_grupos_tarifarios()
    {
        $ignore[] = 'id_grupo_tarifario';
        $this->filter_state($ignore, TRUE, null, false);
        $this->db->select('g.id_grupo_tarifario, g.descricao, g.ncm_recomendada');

        $this->db->join('grupo_tarifario g', 'g.id_grupo_tarifario = i.id_grupo_tarifario', 'inner');
        $this->db->group_by('g.id_grupo_tarifario');

        $query = $this->db->get('cad_item i');

        return $query->result();
    }

    private function filter_state($ignore = [], $join_item = TRUE, $homologacoes = NULL, $showDescricaoMercadoLocal = TRUE, $exportar = FALSE)
    {
        $this->load->model('empresa_model');
        $id_empresa = sess_user_company();
        $empresa = $this->empresa_model->get_entry($id_empresa);
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);
        $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $data['campos_adicionais']);

        // Filtro usuário atribuido com Perfil de tipo de homologação.
        $atribuido_para = $this->get_state('filter.atribuido_para');

        /*
         * Utilizaremos o valor padr? "Engenharia" para os casos do usu?io ser
         * Sysadmin ou Engenheiro, para n? ocorrer erros caso exista algum usu?io com
         * um perfil novo/diferente no futuro.
         */
        $tipo_atribuido_perfil = 'Engenharia';
        if (customer_has_role('fiscal', $atribuido_para)) {
            $tipo_atribuido_perfil = 'Fiscal';
        }

        $hasJoinCadItemHomologacao = false;
        if ($this->get_state('filter.atribuido_para') && $this->get_state('filter.atribuido_para') != -1) {
            //    $this->db->join('cad_item_homologacao hom', 'hom.id_item = i.id_item', 'left');
            $join_item = true;
        }

        $temCriadoPor = false;

        // if ($this->get_state('filter.criado_por') && $this->get_state('filter.criado_por') > 0) {
        //     $temCriadoPor = true;
        // }

        // if ($this->get_state('filter.owner_usuario') && !empty($this->get_state('filter.owner_usuario'))) 
        // {
        //     if ($temCriadoPor) {
        //         $this->db->where(" (`item`.`criado_por` =  '".$this->get_state('filter.criado_por')."'
        //             or `item`.`cod_owner` =  '".$this->get_state('filter.owner_usuario')."') ", NULL, FALSE);
        //     } else {
        //         $this->db->where('item.cod_owner', $this->get_state('filter.owner_usuario'));
        //     }
        // } elseif($temCriadoPor){
        //     $this->db->where('item.criado_por', $this->get_state('filter.criado_por'));
        // }

        if ($busca = $this->get_state('filter.busca')) {
            if (!empty($busca)) {
                $adwhere = ' (';

                if (is_array($busca)) {
                    foreach ($busca as $k => $str) {
                        if ($k > 0) $adwhere .= ' OR ';
                        $adwhere .= " ( i.descricao_mercado_local like '%{$str}%' OR i.subsidio like '%{$str}%' ) ";
                    }
                }

                $adwhere .= ')';

                $this->db->where($adwhere, NULL, FALSE);
            }
        }

        $busca_part_number = $this->get_state('filter.busca_part_number');
        $busca_descricao = $this->get_state('filter.busca_descricao');

        $part_numbers = $this->get_state('filter.part_numbers');

        $adwhere = "";

        // if ($generic_part_numbers = $this->get_state('filter.generic_part_numbers')) {
        //     foreach ($generic_part_numbers as $key => $generic_part_number) {
        //         $generic_part_number = addslashes($generic_part_number);
        //         $adwhere .= (!empty($adwhere)) ? ' OR ' : '';

        //         if ($busca_part_number) {
        //             $adwhere .= 'i.part_number LIKE "' . $generic_part_number . '"';
        //         }

        //         if ($busca_descricao) {
        //             $adwhere .= !empty($adwhere) ? ' OR ' : '';
        //             $adwhere .= 'i.descricao_mercado_local LIKE "' . $generic_part_number . '"';
        //         }
        //     }
        // }

        $part_numbers_str = "";
        $adwhere = "";
        $open_stm = "";
        $close_stm = "";
        $adwhere_go = "";

        if ($this->get_state('filter.part_numbers')) {

            if (is_array($part_numbers)) {

                foreach ($part_numbers as $part_number) {

                    $part_number = str_replace("*", "%", $part_number);

                    $part_number = rtrim($part_number, " ");

                    if ($busca_part_number && $part_number !== '') {
                        $adwhere_go .= 'i.part_number LIKE "' . $part_number  . '" OR ';
                    }

                    if ($busca_descricao && $part_number !== '') {
                        $adwhere_go .= 'i.descricao_mercado_local LIKE "' . $part_number  . '" OR ';
                    }
                }
                $adwhere_go = rtrim($adwhere_go, 'OR ');

                $adwhere .= ' (' . $adwhere_go . ')';
            } else {
                if (strpos($part_numbers, "\n") !== false) {
                    $part_numbers = str_replace(array("\t", "\r\n", "\s", "\n"), '#', $part_numbers);

                    $keywords = explode('#', $part_numbers);

                    foreach ($keywords as $keyword) {

                        $keyword = str_replace("*", "%", $keyword);

                        $keyword = rtrim($keyword, " ");

                        if ($busca_part_number && $keyword !== '') {
                            $adwhere_go .= 'i.part_number LIKE "' . $keyword  . '"';
                            $adwhere_go .= ' OR item.pn_primario_mpn LIKE "' . $keyword  . '"';
                            $adwhere_go .= ' OR item.pn_secundario_ipn LIKE "' . $keyword  . '" OR ';
                        }

                        if ($busca_descricao && $keyword !== '') {
                            $adwhere_go .= 'i.descricao_mercado_local LIKE "' . $keyword  . '" OR ';
                        }
                    }
                    $adwhere_go = rtrim($adwhere_go, 'OR ');

                    $adwhere .= ' (' . $adwhere_go . ')';
                } else {
                    if (strpos($part_numbers, '*') !== false) {

                        if (substr_count($part_numbers, '*') == 1) {

                            if (substr($part_numbers, 0, 1) === '*') {

                                $part_numbers = str_replace("*", "", $part_numbers);

                                if ($busca_part_number) {
                                    $adwhere_go .= 'i.part_number LIKE "%' . $part_numbers . '"';
                                }

                                if ($busca_descricao) {
                                    $adwhere_go .= !empty($adwhere_go) ? ' OR ' : '';
                                    $adwhere_go .= 'i.descricao_mercado_local LIKE "%' . $part_numbers . '"';
                                }

                                $adwhere .= ' (' . $adwhere_go . ')';
                            } elseif (substr($part_numbers, -1) === '*') {
                                $part_numbers = str_replace("*", "", $part_numbers);

                                if ($busca_part_number) {
                                    $adwhere_go .= 'i.part_number LIKE "' . $part_numbers . '%"';
                                }

                                if ($busca_descricao) {
                                    $adwhere_go .= !empty($adwhere_go) ? ' OR ' : '';
                                    $adwhere_go .= 'i.descricao_mercado_local LIKE "' . $part_numbers . '%"';
                                }
                                $adwhere .= ' (' . $adwhere_go . ')';
                            } else {
                                $part_numbers = str_replace("*", "%", $part_numbers);

                                if ($busca_part_number) {
                                    $adwhere_go .= 'i.part_number LIKE "' . $part_numbers . '"';
                                }

                                if ($busca_descricao) {
                                    $adwhere_go .= !empty($adwhere_go) ? ' OR ' : '';
                                    $adwhere_go .= 'i.descricao_mercado_local LIKE "' . $part_numbers . '"';
                                }
                                $adwhere .= ' (' . $adwhere_go . ')';
                            }
                        }
                        if (substr($part_numbers, 0, 1) === '*' && substr($part_numbers, -1) === '*' && substr_count($part_numbers, '*') <= 2) {
                            $part_numbers = str_replace("*", "", $part_numbers);
                            if ($busca_part_number) {
                                $adwhere_go .= 'i.part_number LIKE "%' . $part_numbers . '%"';
                            }

                            if ($busca_descricao) {
                                $adwhere_go .= !empty($adwhere_go) ? ' OR ' : '';
                                $adwhere_go .= 'i.descricao_mercado_local LIKE "%' . $part_numbers . '%"';
                            }

                            $adwhere .= ' (' . $adwhere_go . ')';
                        } else {

                            if (substr_count($part_numbers, '*') >= 2) {

                                $part_numbers = str_replace("*", "%", $part_numbers);

                                if ($busca_part_number && $part_numbers !== '') {
                                    $adwhere_go .= 'i.part_number LIKE "' . $part_numbers  . '" OR ';
                                }

                                if ($busca_descricao && $part_numbers !== '') {
                                    $adwhere_go .= 'i.descricao_mercado_local LIKE "' . $part_numbers  . '" OR ';
                                }

                                $adwhere_go = rtrim($adwhere_go, 'OR ');

                                $adwhere .= ' (' . $adwhere_go . ')';
                            }
                        }
                    } else {

                        if (strpos($part_numbers, ',') !== false) {
                            if ($busca_part_number && $part_numbers != "*") {
                                $keywords = explode(',', $part_numbers);

                                foreach ($keywords as $keyword) {
                                    if ($busca_part_number && $keyword !== '') {
                                        $adwhere_go .= 'i.part_number LIKE "%' . $keyword  . '%" OR ';
                                    }
                                }
                                $adwhere_go = rtrim($adwhere_go, 'OR ');
                                $adwhere .= ' (' . $adwhere_go . ')';
                            }
                        } else {
                            if ($busca_part_number) {
                                // $adwhere .= 'i.part_number LIKE "' . $part_numbers . '"';
                                $adwhere .= 'i.part_number LIKE "' . $part_numbers . '"';
                                $adwhere .= ' OR item.pn_primario_mpn LIKE "' . $part_numbers  . '"';
                                $adwhere .= ' OR item.pn_secundario_ipn LIKE "' . $part_numbers  . '"';
                            }

                            if ($busca_descricao) {
                                $adwhere .= !empty($adwhere) ? ' OR ' : '';
                                $adwhere .= 'i.descricao_mercado_local LIKE "' . $part_numbers . '"';
                            }

                            $adwhere = ' (' . $adwhere . ')';
                        }
                    }
                }
            }
        }

        // if($hasPnPrimarioSecundario) {
        //     $adwhere .= (!empty($adwhere)) ? ' OR ' : '';
        //     $adwhere .= 'item.pn_primario_mpn IN (' . $part_numbers_str . ')';

        //     $adwhere .= (!empty($adwhere)) ? ' OR ' : '';
        //     $adwhere .= 'item.pn_secundario_ipn IN (' . $part_numbers_str . ')';
        // }

        if (!empty($adwhere) && $adwhere !=  ' ()') {
            $this->db->where($adwhere, NULL, FALSE);
        }
        if ($owner_filter = $this->get_state('filter.owner')) {
            $this->db->where_in('item.cod_owner', $owner_filter);
        }
        if ($this->get_state('filter.status_exportacao')) {
            $this->db->where_in('i.status_exportacao', $this->get_state('filter.status_exportacao'));
        }

        //Somente acesso aos itens da empresa do usuário
        if ($this->get_state('filter.id_empresa')) {
            $this->db->where('i.id_empresa', $this->get_state('filter.id_empresa'), FALSE);
        }

        if (!in_array('id_grupo_tarifario', $ignore) && $this->get_state('filter.id_grupo_tarifario')) {
            $this->db->where('i.id_grupo_tarifario', $this->get_state('filter.id_grupo_tarifario'));
        }

        $list_opt = $this->get_state('filter.list_opt');

        // $sub_where = '(';
        // if ($list_opt !== 'revisao' && $list_opt !== 'todos'  && $list_opt != 'homologar') {
        //     if (isset($homologacoes['homologacao_fiscal']) && $homologacoes['homologacao_fiscal'] == TRUE) {
        //         $sub_where .= ' i.id_resp_fiscal IS NOT NULL ';
        //     }
        //     if ((isset($homologacoes['homologacao_fiscal']) && $homologacoes['homologacao_fiscal'] == TRUE)
        //         && (isset($homologacoes['homologacao_engenharia']) && $homologacoes['homologacao_engenharia'] == TRUE)
        //     ) {
        //         $sub_where .= ' AND ';
        //     }

        //     if (isset($homologacoes['homologacao_engenharia']) && $homologacoes['homologacao_engenharia'] == TRUE) {
        //         $sub_where .= ' i.id_resp_engenharia IS NOT NULL ';
        //     }
        //     $sub_where .= ')';
        //     if ($sub_where != '()') {
        //         $this->db->where($sub_where);
        //     }
        // }

        if ($list_opt === "pendente_informacoes") {
            $list_opt = "pendente_duvidas";
        }


        if ($this->get_state('filter.habilitar_pr')) {
            if ($list_opt != 'pendente_duvidas' && !empty($list_opt)) {
                $this->db->where('not exists(SELECT * FROM ctr_pendencias_pergunta pp WHERE i.part_number = pp.part_number AND pp.id_empresa = i.id_empresa AND pp.estabelecimento = i.estabelecimento AND pp.pendente = 1)', NULL, FALSE);
            }
        }

        if (!empty($list_opt) && $list_opt != "todos") {
            if ($exportar == TRUE) {
                if ($status_id = $this->get_state('filter.list_opt_id')) {
                    $this->db->where_in("item.id_status", $status_id);
                }
            } else {
                $this->db->where_in("s.slug", array($list_opt));
            }
        } else {
            $hasJoinCadItemHomologacao = true;
            if ($atribuido_para != -1 && $atribuido_para != null) {
                $this->db->where("(
                        (s.slug = 'homologar'
                            AND (
                                (hom.id_usuario <> {$atribuido_para} OR hom.id_usuario IS NULL)
                                AND (
                                    (item.id_resp_fiscal = {$atribuido_para} OR item.id_resp_engenharia = {$atribuido_para})
                                    OR (i.id_resp_fiscal = {$atribuido_para} OR i.id_resp_engenharia = {$atribuido_para})
                                )
                            )
                        )
                        OR (
                            s.slug = 'homologado'
                            AND (
                                (item.id_resp_fiscal = {$atribuido_para} OR item.id_resp_engenharia = {$atribuido_para})
                                OR (i.id_resp_fiscal = {$atribuido_para} OR i.id_resp_engenharia = {$atribuido_para})
                            )
                            AND i.status_exportacao IN (0, 1)
                            AND hom.homologado = 1
                        )
                        OR (
                            s.slug = 'nao_homologado'
                            AND (
                                item.id_resp_fiscal = {$atribuido_para}
                                OR item.id_resp_engenharia = {$atribuido_para}
                                OR (
                                    i.id_resp_fiscal = {$atribuido_para}
                                    OR i.id_resp_engenharia = {$atribuido_para}
                                )
                                AND (
                                    i.id_item NOT IN (
                                        SELECT cad_hom.id_item
                                        FROM cad_item_homologacao AS cad_hom
                                        INNER JOIN cad_item AS cad ON cad.id_item = cad_hom.id_item
                                        WHERE cad.id_empresa = {$id_empresa} AND cad_hom.id_usuario = {$atribuido_para}
                                    )
                                )
                            )
                        )
                        OR (
                            s.slug = 'obsoleto'
                            AND (
                                (i.id_resp_fiscal = {$atribuido_para} OR i.id_resp_engenharia = {$atribuido_para})
                                AND i.descricao_mercado_local IS NOT NULL
                            )
                        )
                        OR (
                            s.slug = 'revisao'
                            AND (
                                i.id_item NOT IN (
                                    SELECT cad_hom.id_item
                                    FROM cad_item_homologacao AS cad_hom
                                    INNER JOIN cad_item AS cad ON cad.id_item = cad_hom.id_item
                                    WHERE cad.id_empresa = {$id_empresa} AND cad_hom.id_usuario = {$atribuido_para}
                                )
                                AND (i.id_resp_fiscal = {$atribuido_para} OR i.id_resp_engenharia = {$atribuido_para})
                            )
                        )
                    )

                    AND (i.id_item NOT IN (SELECT 
                    cad_hom.id_item
                    FROM
                        cad_item_homologacao AS cad_hom
                            INNER JOIN
                        cad_item AS cad ON cad.id_item = cad_hom.id_item
                    WHERE
                        cad.id_empresa = {$id_empresa}
                            AND cad_hom.id_usuario = {$atribuido_para})
                    AND (i.id_resp_fiscal = {$atribuido_para}
                    OR i.id_resp_engenharia = {$atribuido_para}))
                    ", false, false);
            } else {

                $this->db->where("(
                    s.slug = 'homologar'
                    OR (
                        s.slug = 'homologado' 
                        AND i.status_exportacao IN (0, 1)
                        AND hom.homologado = 1
                    )
                    OR s.slug = 'nao_homologado'
                    OR s.slug = 'obsoleto'
                    OR s.slug IN ('revisao', 'homologado_em_revisao')
                )", false, false);
            }
        }

        if ($this->get_state('filter.atribuido_para') && $this->get_state('filter.atribuido_para') != -1) {

            if ($list_opt == 'homologar') {
                $hasJoinCadItemHomologacao = true;
                $this->db->where("(( hom.id_usuario <> {$atribuido_para} OR hom.id_usuario is null ) AND ((item.id_resp_fiscal = {$atribuido_para} OR item.id_resp_engenharia = {$atribuido_para}) OR (i.id_resp_fiscal = {$atribuido_para} OR i.id_resp_engenharia = {$atribuido_para})))", FALSE, FALSE);
            } else  if ($list_opt == 'homologado') {
                $hasJoinCadItemHomologacao = true;
                $this->db->where("((item.id_resp_fiscal = {$atribuido_para} OR item.id_resp_engenharia = {$atribuido_para}) OR (i.id_resp_fiscal = {$atribuido_para} OR i.id_resp_engenharia = {$atribuido_para}) AND hom.homologado = 1 )", FALSE, FALSE);
            } else if ($list_opt != "todos") {
                $this->db->where(" (i.id_item not in (SELECT id_item from cad_item_homologacao  WHERE id_usuario  = {$atribuido_para}) )", FALSE, FALSE);
                $this->db->where($atribuido_para . ' IN (i.id_resp_fiscal, i.id_resp_engenharia)', FALSE, FALSE);
            }
        }

        // if ($this->get_state('filter.atribuido_para') && $this->get_state('filter.atribuido_para') != -1) {
        // } else

        if ($showDescricaoMercadoLocal && ($has_descricao = $this->get_state('filter.has_descricao_mercado_local') || $list_opt != 'revisao' && $list_opt != 'homologar' && $list_opt !== 'todos')) {
            $this->db->where('i.descricao_mercado_local IS NOT NULL', NULL, TRUE);
        }

        if ($status_implementacao = $this->get_state('filter.status_implementacao')) {
            $this->db->where_in('i.status_implementacao', $status_implementacao);
        }

        if ($status_simplus = $this->get_state('filter.status_simplus')) {
            if (in_array('0', $status_simplus)) {
                $this->db->where('(i.status_simplus IS NULL or i.status_simplus IN (' . implode(', ', $status_simplus) . '))', null, false);
            } else {
                $this->db->where_in('i.status_simplus', $status_simplus);
            }
        }

        $this->db->join('cad_item_homologacao hom', 'hom.id_item = i.id_item', 'left');

        if ($join_item == TRUE) {
            if ($this->get_state('filter.use_index_helper') == TRUE) {
                $this->db->join('item', 'i.part_number = item.part_number AND i.id_empresa = item.id_empresa AND i.estabelecimento = item.estabelecimento', 'inner', false);
            } else {
                $this->db->join('item', 'i.part_number = item.part_number AND i.id_empresa = item.id_empresa AND i.estabelecimento = item.estabelecimento', 'inner', false);
            }

            if (!is_null($this->get_state('filter.estabelecimento'))) {
                $estabelecimento = $this->get_state('filter.estabelecimento');

                if (is_array($estabelecimento)) {
                    $this->db->where_in('item.estabelecimento', $estabelecimento, FALSE);
                } else if ($estabelecimento != "" && !is_array($estabelecimento)) {
                    $this->db->where('item.estabelecimento', $estabelecimento, FALSE);
                }
            }

            if ($prioridade = $this->get_state("filter.prioridade")) {
                $this->db->where_in("item.id_prioridade", $prioridade);
            }
        }

        if ($this->get_state('filter.evento')) {
            $evento = $this->get_state('filter.evento');

            if (!is_array($evento)) {
                $evento = array($evento);
            }

            $newEvento = array_filter($evento, function ($item) {
                return $item != "sem_evento";
            });

            $eventoConcatenado = "";

            foreach ($newEvento as $item) {
                if (empty($eventoConcatenado)) {
                    $eventoConcatenado .= "'{$item}'";
                } else {
                    $eventoConcatenado .= ",'{$item}'";
                }
            }

            if (!empty($newEvento)) {
                if (in_array("sem_evento", $evento)) {
                    $this->db->where("(item.evento IN ({$eventoConcatenado}) OR (item.evento = '' OR item.evento IS NULL))", null, false);
                } else {
                    $this->db->where("(item.evento IN ({$eventoConcatenado}))", null, false);
                }
            } elseif (in_array("sem_evento", $evento)) {
                $this->db->where("(item.evento = '' OR item.evento IS NULL)", NULL, FALSE);
            }
        }

        $integracao = $this->get_state('filter.integracao');
        if ($integracao) {
            if ($integracao == 10) { // Pendente de integração
                $this->db->where('item.id_status', 10);
            } elseif ($integracao == 1001) { // Integrado
                $this->db->where('item.id_status !=', 10);
            }
        }

        // Filtro de Status de Atributos
        $filter_status_atributos = $this->get_state('filter.status_atributos');
        if ($filter_status_atributos) {
            $this->db->where_in("item.wf_status_atributos", $filter_status_atributos);
        }

        $status_preenchimento = $this->get_state('filter.status_preenchimento');
        if ($status_preenchimento) {
            $this->db->where_in("item.status_attr", $status_preenchimento);
        }

        // Filtro de Estabelecimento
        $estabelecimento_modal = $this->get_state('filter.estabelecimento_modal');
        if ($estabelecimento_modal) {
            $this->db->where_in("item.estabelecimento", $estabelecimento_modal);
        }

        // Filtro de NCM Proposto
        $ncm_proposta_modal = $this->get_state('filter.ncm_proposta_modal');
        if ($ncm_proposta_modal) {
            $this->db->where_in("i.ncm_proposto", $ncm_proposta_modal);
        }

        // Filtro de data de início de homologação
        if ($data_inicio_homologacao_modal = $this->get_state('filter.data_inicio_homologacao_modal')) {
            $this->db->where('date(hom.criado_em) >=', $data_inicio_homologacao_modal);
        }

        // Filtro de data de fim de homologação
        if ($data_fim_homologacao_modal = $this->get_state('filter.data_fim_homologacao_modal')) {
            $this->db->where('date(hom.criado_em) <=', $data_fim_homologacao_modal);
        }

        $this->db->join('status s', 's.id=item.id_status', 'inner');
    }

    public function get_entries_by_user_count($homologacoes)
    {
        $this->filter_state(array(), TRUE, $homologacoes);

        $select = '
            i.id_item,
            i.id_resp_engenharia,
            i.part_number,
            i.id_empresa
        ';


        $this->db->select($select, false);

        if (!$this->get_state('filter.semanal')) {
            $this->db->where('envia_email_notificacao is null', NULL, TRUE);
        }

        $this->db->where('i.descricao_mercado_local IS NOT NULL', NULL, TRUE);

        $this->db->where('NOT EXISTS (SELECT 1 FROM cad_item_homologacao cih WHERE cih.id_item = i.id_item AND tipo_homologacao = "Engenharia")', NULL, FALSE);

        $query = $this->db->get($this->_table . ' i');

        return $query->result();
    }

    public function get_entries($limit = null, $offset = null, $count = FALSE, $export_data = FALSE, $homologacoes = NULL, $view_homologacao = FALSE, $only_events = FALSE, $owner_user = NULL, $part_numbers_created_for_user = NULL)
    {
        $this->filter_state(array(), TRUE, $homologacoes);

        $select  = 'COALESCE(item.part_number_similar, item.part_number) as coalesce_part_number, ';
        $select .= "
            i.id_item,
            i.part_number,
            i.id_empresa,
            i.descricao_mercado_local,
            i.ncm_proposto,
            i.caracteristicas,
            i.subsidio, i.dispositivo_legal,
            i.solucao_consulta,
            i.id_grupo_tarifario,
            i.id_resp_fiscal,
            i.id_resp_engenharia,
            i.status_simplus,
            i.status_exportacao,
            i.cod_cest,
            i.status_homologacao,
            i.houve_descricao_manual,
            item.part_number_similar,
            item.peso,
            item.prioridade,
            item.descricao as descricao_atual,
            item.ncm as ncm_atual,
            item.ncm_fornecedor,
            item.id_status,
            item.estabelecimento,
            item.item_ja_homologado,
            item.cod_owner,
            (
                SELECT o.codigo
                FROM `owner` o
                WHERE `item`.`cod_owner` = `o`.`codigo`
                LIMIT 1
            ) AS owner_codigo,
            (
                SELECT o.descricao
                FROM `owner` o
                WHERE `item`.`cod_owner` = `o`.`codigo`
                LIMIT 1
            ) AS owner_descricao,
            item.evento as evento,
            s.slug,
            i.suframa_destaque,
            i.suframa_ppb,
            i.suframa_descricao,
            i.suframa_produto,
            i.suframa_codigo,
            i.li_orgao_anuente,
            i.li,
            i.antidumping,
            i.li_destaque,
            ep.nome as empresa_prioridade,
            (
                SELECT GROUP_CONCAT(DISTINCT rg.nome SEPARATOR ', ')
                FROM `owner_usuario` ou
                JOIN `usuario` rg ON `ou`.`id_usuario` = `rg`.`id_usuario`
                WHERE `item`.`cod_owner` = `ou`.`id_owner`
                    AND ou.responsavel_gestor = 1
            ) AS responsaveis_gestores_nomes,
            (
                SELECT GROUP_CONCAT(DISTINCT rg.email SEPARATOR ', ')
                FROM `owner_usuario` ou
                JOIN `usuario` rg ON `ou`.`id_usuario` = `rg`.`id_usuario`
                WHERE `item`.`cod_owner` = `ou`.`id_owner`
                    AND ou.responsavel_gestor = 1
            ) AS responsaveis_gestores_emails,
            (
                SELECT GROUP_CONCAT(
                    CONCAT(cia.atributo, ':|:', cia.apresentacao, ' - ', 
                        COALESCE(NULLIF(cia.descricao, ''), cia.codigo))
                    SEPARATOR '||'
                )
                FROM `cad_item_attr` cia
                WHERE cia.id_item = i.id_item
            ) AS atributos
        ";

        // // Faça JOIN com a tabela owner
        // $this->db->join('owner o', 'item.cod_owner = o.codigo', 'left');

        // // Faça JOIN com as tabelas owner_usuario e usuario para buscar os responsáveis gestores
        // $this->db->join('owner_usuario ou', 'o.id_owner = ou.id_owner AND ou.responsavel_gestor = 1', 'left');
        // $this->db->join('usuario rg', 'ou.id_usuario = rg.id_usuario', 'left');

        $this->db->join('empresa_prioridades ep', 'ep.id_prioridade = item.id_prioridade', 'left');

        $this->db->order_by('i.part_number', 'ASC');

        if ($export_data === TRUE) {
            $select .= ',
                i.memoria_classificacao,
                i.num_ex_ii,
                i.num_ex_ipi,
                i.funcao,
                i.inf_adicionais,
                i.aplicacao,
                i.marca,
                i.material_constitutivo,
                i.status_implementacao,
                i.motivo_implementacao,
                item.evento,
                item.tag,
                item.item_ja_homologado,
                item.descricao_proposta_completa,
                g.descricao as grupo_tarifario,
                e.razao_social,
                count(cnve.nve_atributo) as has_nve
            ';

            $this->db->join('cad_item_nve cnve', 'cnve.id_item = i.id_item', 'left');
            $this->db->join('grupo_tarifario g', 'g.id_grupo_tarifario = i.id_grupo_tarifario', 'inner');
            $this->db->join('empresa e', 'e.id_empresa = i.id_empresa', 'inner');
        }

        if ($this->get_state('filter.inner_with_logs')) {
            $this->db->join('item_log log', 'i.id_item = log.id_item', 'inner');
            $select .= ", MAX(log.criado_em) as ultimo_log";
            $this->db->group_by('i.id_item', TRUE);
        }

        if ($this->get_state('filter.group_similars')) {
            $this->db->group_by('coalesce_part_number, item.estabelecimento', TRUE);
        }

        if ($only_events == TRUE) {
            $this->db->select('item.evento', false);
            $this->db->group_by('item.evento');
            $this->db->order_by('item.evento', 'ASC');
            $query = $this->db->get($this->_table . ' i', null, null);
            return $query->result();
        }

        $owner_filter = $this->get_state('filter.owner');
        if ($owner_filter) {
            $this->db->where_in('item.cod_owner', $owner_filter);
        }

        if ($part_numbers_created_for_user) {
            $this->db->where_in('item.part_number', $part_numbers_created_for_user);
        }

        if ($owner_user) {
            $this->db->or_where('item.cod_owner', $owner_user);
        }

        $integracao = $this->get_state('filter.integracao');
        if ($integracao) {
            if ($integracao == 10) { // Pendente de integração
                $this->db->where('item.id_status', 10);
            } elseif ($integracao == 1001) { // Integrado
                $this->db->where('item.id_status !=', 10);
            }
        }

        $integracao_ecomex = $this->get_state('filter.integracao_ecomex');
        if ($integracao_ecomex == 10) { // Nacional
            $this->db->where("(item.indicador_ecomex = 'EN' OR item.indicador_ecomex = 'NE')", null, false);
        } elseif ($integracao_ecomex == 1001) { // Importação
            $this->db->where('item.indicador_ecomex', 'EI');
        }

        if ($this->get_state('filter.evento')) {
            $evento = $this->get_state('filter.evento');

            if (!is_array($evento)) {
                $evento = array($evento);
            }

            $newEvento = array_filter($evento, function ($item) {
                return $item != "sem_evento";
            });

            $eventoConcatenado = "";

            foreach ($newEvento as $item) {
                if (empty($eventoConcatenado)) {
                    $eventoConcatenado .= "'{$item}'";
                } else {
                    $eventoConcatenado .= ",'{$item}'";
                }
            }

            if (!empty($newEvento)) {
                if (in_array("sem_evento", $evento)) {
                    $this->db->where("(item.evento IN ({$eventoConcatenado}) OR (item.evento = '' OR item.evento IS NULL))", null, false);
                } else {
                    $this->db->where("(item.evento IN ({$eventoConcatenado}))", null, false);
                }
            } elseif (in_array("sem_evento", $evento)) {
                $this->db->where("(item.evento = '' OR item.evento IS NULL)", NULL, FALSE);
            }
        }

        if ($count) {
            $this->db->group_by('i.id_item');

            $total = $this->db->get($this->_table . ' i');

            return $total->num_rows();
        } else {
            $this->db->select($select, false);

            $this->db->join('cad_item_attr cia', 'cia.id_item = i.id_item', 'left');
            $this->db->group_by('i.id_item');

            $query = $this->db->get($this->_table . ' i', $limit, $offset);

            return $this->get_state("filter.use_unbuffered_query") ? $query : $query->result();
        }
    }

    public function get_total_entries($homologacoes = NULL, $owner_user = NULL, $part_numbers_created_for_user = NULL)
    {
        return $this->get_entries(NULL, NULL, TRUE, NULL, $homologacoes, FALSE, FALSE, $owner_user, $part_numbers_created_for_user);
    }

    public function get_all_entries()
    {
        $this->filter_state(array(), TRUE);

        $this->db->select('i.id_item');
        $query = $this->db->get($this->_table . ' i');
        return $query->result();
    }

    public function get_planilha_upload_data_xls($idItens)
    {
        if (empty($idItens)) {
            $this->filter_state(array(), TRUE);
        } else {
            $this->db->join('item', 'i.part_number = item.part_number AND i.id_empresa = item.id_empresa AND i.estabelecimento = item.estabelecimento', 'inner');
        }

        $this->db->select(
            "
            i.id_item,
            i.part_number,
            i.estabelecimento,
            i.descricao_mercado_local,
            i.id_grupo_tarifario,
            g.descricao as grupo_tarifario,
            i.caracteristicas,
            i.subsidio,
            COALESCE(ruf.email, 'Usuário não encontrado') as resp_usuario_fiscal,
            COALESCE(rue.email, 'Usuário não encontrado') as resp_usuario_engenharia,
            i.memoria_classificacao,
            item.evento,
            i.num_ex_ii,
            i.num_ex_ipi,
            i.dispositivo_legal,
            i.solucao_consulta,
            i.funcao,
            i.inf_adicionais,
            i.aplicacao,
            i.marca,
            i.suframa_destaque,
            i.suframa_ppb,
            i.suframa_descricao,
            i.suframa_produto,
            i.suframa_codigo,
            i.li_destaque,
            i.status_exportacao,
            i.li_orgao_anuente,
            i.li,
            i.antidumping,
            item.descricao_proposta_completa,
            item.is_drawback,
            i.material_constitutivo,
            i.cod_cest,
            item.ncm as ncm_atual,
            item.ncm_fornecedor,
            i.ncm_proposto,
            item.descricao       as descricao_atual,
            huf.email            as homolog_fiscal_email,
            hue.email            as homolog_engenharia_email,
            v_item_status.status_formatado as item_status,
            cihf.homologado     as homolog_fiscal_status,
            cihe.homologado     as homolog_engenharia_status,
            cihf.motivo         as homolog_fiscal_motivo,
            cihe.motivo         as homolog_engenharia_motivo",
            false
        );

        // v_item_status
        $this->db->join('v_item_status', 'v_item_status.id_empresa = i.id_empresa and v_item_status.part_number = i.part_number and v_item_status.estabelecimento   = i.estabelecimento', 'inner');

        // grupo_tarifario
        $this->db->join('grupo_tarifario g', 'g.id_grupo_tarifario = i.id_grupo_tarifario', 'inner');

        // responsáveis
        $this->db->join('usuario ruf', 'ruf.id_usuario = i.id_resp_fiscal',     'left');
        $this->db->join('usuario rue', 'rue.id_usuario = i.id_resp_engenharia', 'left');

        // responsáveis homologação
        $this->db->join('cad_item_homologacao cihf', 'cihf.id_item = i.id_item and cihf.tipo_homologacao = "Fiscal"', 'left');
        $this->db->join('usuario huf', 'huf.id_usuario = cihf.id_usuario', 'left');

        $this->db->join('cad_item_homologacao cihe', 'cihe.id_item = i.id_item and cihe.tipo_homologacao = "Engenharia"', 'left');
        $this->db->join('usuario hue', 'hue.id_usuario = cihe.id_usuario', 'left');

        if (!empty($idItens)) {
            $this->db->where_in('i.id_item', $idItens);
        }

        $this->db->order_by('i.part_number', 'asc');

        $this->db->group_by('i.id_item');

        $query = $this->db->get('cad_item i');

        return $query->result();
    }

    // salvar atributos
    public function save_status_attr($idItem, $ncmProposto, $delete = true)
    {
        if (empty($idItem)) {
            return;
        }

        $this->limpar_atributos_nao_relacionados($idItem, $ncmProposto,  $delete);

        if (empty($idItem)) {
            return;
        }

        $query =  $this->db->query("SELECT 
                (SELECT 
                        COUNT(*)
                    FROM
                        comex cm
                    WHERE
                        ind_ecomex = 'EI'
                            AND cm.part_number_original = i.part_number
                            AND cm.unidade_negocio = i.estabelecimento
                            AND cm.id_empresa = i.id_empresa
                    LIMIT 1) AS tipo_item,
                ci.part_number,
                ci.estabelecimento,
                ci.id_empresa,
                CASE
                    WHEN
                        EXISTS( SELECT 
                                1
                            FROM
                                ncm_atributo n
                            WHERE
                                n.ncm = ci.ncm_proposto
                                    AND (n.codigo = 'null'))
                    THEN
                        '5'
                    WHEN
                        COUNT(*) = 0
                            OR COUNT(CASE
                            WHEN attr.codigo IS NULL OR attr.codigo = '' THEN 1
                        END) = COUNT(*)
                    THEN
                        '1'
                    WHEN
                        COUNT(CASE
                            WHEN
                                attr.obrigatorio = 1
                                    AND (attr.codigo IS NULL OR attr.codigo = '')
                            THEN
                                1
                        END) > 0
                    THEN
                        '2'
                    WHEN
                        COUNT(CASE
                            WHEN
                                (attr.obrigatorio = 0
                                    AND (attr.codigo IS NULL OR attr.codigo = ''))
                                    OR (attr.id_item IS NULL)
                            THEN
                                1
                            ELSE NULL
                        END) > 0
                    THEN
                        '3'
                    WHEN
                        COUNT(CASE
                            WHEN
                                attr.codigo IS NOT NULL
                                    AND attr.codigo <> ''
                            THEN
                                1
                        END) = COUNT(*)
                    THEN
                        '4'
                    ELSE '0'
                END AS status_preenchimento
            FROM
                cad_item ci
                    LEFT JOIN
                cad_item_attr attr ON ci.id_item = attr.id_item
                    INNER JOIN
                item i ON ci.part_number = i.part_number
                    AND ci.estabelecimento = i.estabelecimento
                    AND ci.id_empresa = i.id_empresa

            WHERE ci.id_item = '{$idItem}'
            GROUP BY ci.id_item");

        while ($item = $query->unbuffered_row()) {
            if (empty($item->part_number) || empty($item->estabelecimento) || empty($item->id_empresa) || empty($item->status_preenchimento)) {
                continue;
            }

            $this->db->query(" UPDATE item i
                        SET i.status_attr = '{$item->status_preenchimento}'
                            WHERE i.part_number = '{$item->part_number}' 
                            AND i.estabelecimento = '{$item->estabelecimento}'
                            AND i.id_empresa = '{$item->id_empresa}'
                            AND i.status_attr <> '{$item->status_preenchimento}'");

            // if ($item->tipo_item == 0) {
            //     if ($item->status_preenchimento == 3 || $item->status_preenchimento == 4) {
            //         $this->db->query(" UPDATE item i
            //         SET i.wf_status_atributos = '7'
            //             WHERE i.part_number = '{$item->part_number}' 
            //             AND i.estabelecimento = '{$item->estabelecimento}'
            //             AND i.id_empresa = '{$item->id_empresa}'");
            //     }
            // }
        }
    }

    public function get_homolog_data_xls($idItens)
    {
        // $this->db->query('SET SESSION group_concat_max_len = 20000;');

        $customer_can_nve = customer_can('nve');
        $with_atributos = $this->get_state('filter.with_atributos');
        $with_perguntas_respostas = $this->get_state('filter.with_perguntas_respostas');

        $id_empresa = sess_user_company();
        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry($id_empresa);
        $campos_adicionais = explode("|", $empresa->campos_adicionais);
        $hasOwner =  in_array('owner', $campos_adicionais);
        $list_opt = $this->get_state('filter.list_opt');

        if ($this->get_state('filter.list_opt')) {
            $this->db->where_in('slug', $list_opt);
            $query = $this->db->get('status');
            if ($query->num_rows() > 0) {
                $result = $query->result();
                $ids_status = array_map(function ($status) {
                    return $status->id;
                }, $result);

                $this->set_state('filter.list_opt_id', $ids_status);
            }
        }

        if ($hasOwner) {
            $cnpj_raiz_logado = $this->empresa_model->get_entry_cnpj_raiz($id_empresa);
        }
        if (empty($idItens)) {
            $this->filter_state([], true, null, true, true);
        } else {
            // Este join 'item' é feito antes do SELECT principal, o que é padrão no CI
            if ($this->get_state('filter.use_index_helper') == true) {
                $this->db->join('item', 'i.part_number = item.part_number AND i.id_empresa = item.id_empresa AND i.estabelecimento = item.estabelecimento', 'inner');
            } else {
                $this->db->join('item', 'i.part_number = item.part_number AND i.id_empresa = item.id_empresa AND i.estabelecimento = item.estabelecimento', 'inner');
            }
        }

        $useViewComex = $this->config->item('use_view_comex');

        // *** MODIFICAÇÃO 1: Adicionar o JOIN para nve_attr_counts (para has_nve) ***
        // Este JOIN é adicionado aqui para que esteja disponível para todas as branches do SELECT abaixo.
        $this->db->join(
            '(SELECT cd_nomenc_ncm, COUNT(cd_atributo_ncm) as count_cd_atributo_ncm FROM nve_atributo GROUP BY cd_nomenc_ncm) nve_attr_counts',
            'nve_attr_counts.cd_nomenc_ncm = i.ncm_proposto',
            'left'
        );

        // *** Retirado trecho abaixo para exibir corretamente os atributos condicionados na exportação ***
        // AND cia.atributo IN (SELECT codigo FROM ncm_atributo WHERE ncm =  i.ncm_proposto)
        if ($with_atributos) {
            $this->db->select("(SELECT GROUP_CONCAT(
                DISTINCT CONCAT(
                    ' |:| ', cia.atributo,
                    ' || ', COALESCE(cia.apresentacao, ''),
                    ' - ', COALESCE(NULLIF(cia.descricao, ''),
                    CASE
                        WHEN cia.codigo = '0' THEN 'Não'
                        WHEN cia.codigo = '1' THEN 'Sim'
                        ELSE cia.codigo
                    END)
                ) SEPARATOR '||')
                FROM cad_item_attr cia
                WHERE cia.id_item = i.id_item
            ) AS atributos", false);
        }

        if ($with_perguntas_respostas) {
            $this->db->select(
                "
                (SELECT GROUP_CONCAT(DISTINCT cpp.pergunta SEPARATOR '|:| ')
                FROM ctr_pendencias_pergunta cpp
                WHERE cpp.id_empresa = i.id_empresa AND cpp.part_number = i.part_number AND cpp.estabelecimento = i.estabelecimento) AS Perguntas,
                (SELECT GROUP_CONCAT(DISTINCT cr.resposta SEPARATOR '|:| ')
                FROM ctr_resposta cr
                JOIN ctr_pendencias_pergunta cpp ON cpp.id = cr.id_pergunta
                WHERE cpp.id_empresa = i.id_empresa AND cpp.part_number = i.part_number AND cpp.estabelecimento = i.estabelecimento) AS Respostas",
                false
            );
        }

        if ($useViewComex && $this->db->table_exists('vw_comex')) {
            $this->db->select(
                "
                gt.id_grupo_tarifario,
                COUNT(DISTINCT CASE WHEN ex.cod_tipo_ex < 5 THEN ex.num_ex END) AS total_ex,
                COUNT(DISTINCT CASE WHEN ex.cod_tipo_ex > 5 THEN ex.num_ex END) AS total_exipi,
                gt.ncm_recomendada,
                i.id_item,
                i.part_number,
                i.estabelecimento,
                i.descricao_mercado_local,
                i.id_grupo_tarifario,
                gt.descricao as grupo_tarifario,
                i.caracteristicas,
                i.subsidio,
                COALESCE(ruf.email, 'Usuário não encontrado') as resp_usuario_fiscal,
                COALESCE(rue.email, 'Usuário não encontrado') as resp_usuario_engenharia,
                i.memoria_classificacao,
                item.evento,
                i.num_ex_ii,
                i.num_ex_ipi,
                i.dispositivo_legal,
                i.solucao_consulta,
                i.funcao,
                i.inf_adicionais,
                i.aplicacao,
                i.marca,
                i.suframa_destaque,
                i.suframa_ppb,
                i.suframa_descricao,
                i.suframa_produto,
                i.suframa_codigo,
                i.li_orgao_anuente,
                i.li,
                wf.status as status_atributos,
                item.status_attr as status_preenchimento,
                i.antidumping,
                i.li_destaque,
                item.descricao_proposta_completa,
                i.material_constitutivo,
                i.cod_cest,
                item.ncm as ncm_atual,
                i.ncm_proposto,
                item.ncm_fornecedor,
                item.descricao       as descricao_atual,
                item.peso,
                item.prioridade,
                item.lista_cliente,
                item.lista_becomex,
                item.regra_aplicada,
                item.item_ja_homologado,
                item.observacoes,
                item.cod_owner,
                item.descricao_global,
                item.dat_criacao,
                item.status_attr,
                item.data_modificacao,
                item.integracao_novo_material,
                ep.nome as empresa_prioridade,
                vw_comex.ind_ecomex as indicador_ecomex,
                vw_comex.num_di as num_di,
                vw_comex.data_di as data_di,
                vw_comex.ind_drawback as ind_drawback,
                vw_comex.ncm as ncm_ecomex,
                v_item_status.status_formatado as item_status,
                -- Subconsultas de homologação serão tratadas separadamente para melhor performance.
                -- Os joins foram removidos.
                (SELECT MAX(cihf_sub.criado_em)
                FROM cad_item_homologacao cihf_sub
                WHERE cihf_sub.id_item = i.id_item AND cihf_sub.tipo_homologacao = 'Fiscal' AND cihf_sub.homologado = 1) as homolog_fiscal_data,
                (SELECT MAX(cihe_sub.criado_em)
                FROM cad_item_homologacao cihe_sub
                WHERE cihe_sub.id_item = i.id_item AND cihe_sub.tipo_homologacao = 'Engenharia' AND cihe_sub.homologado = 1) as homolog_engenharia_data,
                (SELECT cihf_sub.homologado
                FROM cad_item_homologacao cihf_sub
                WHERE cihf_sub.id_item = i.id_item AND cihf_sub.tipo_homologacao = 'Fiscal'
                ORDER BY cihf_sub.criado_em DESC LIMIT 1) as homolog_fiscal_status,
                (SELECT cihe_sub.homologado
                FROM cad_item_homologacao cihe_sub
                WHERE cihe_sub.id_item = i.id_item AND cihe_sub.tipo_homologacao = 'Engenharia'
                ORDER BY cihe_sub.criado_em DESC LIMIT 1) as homolog_engenharia_status,
                (SELECT cihf_sub.motivo
                FROM cad_item_homologacao cihf_sub
                WHERE cihf_sub.id_item = i.id_item AND cihf_sub.tipo_homologacao = 'Fiscal'
                ORDER BY cihf_sub.criado_em DESC LIMIT 1) as homolog_fiscal_motivo,
                (SELECT cihe_sub.motivo
                FROM cad_item_homologacao cihe_sub
                WHERE cihe_sub.id_item = i.id_item AND cihe_sub.tipo_homologacao = 'Engenharia'
                ORDER BY cihe_sub.criado_em DESC LIMIT 1) as homolog_engenharia_motivo,
                (SELECT huf_sub.email
                FROM cad_item_homologacao cihf_sub
                JOIN usuario huf_sub ON huf_sub.id_usuario = cihf_sub.id_usuario
                WHERE cihf_sub.id_item = i.id_item AND cihf_sub.tipo_homologacao = 'Fiscal'
                ORDER BY cihf_sub.criado_em DESC LIMIT 1) as homolog_fiscal_email,
                (SELECT hue_sub.email
                FROM cad_item_homologacao cihe_sub
                JOIN usuario hue_sub ON hue_sub.id_usuario = cihe_sub.id_usuario
                WHERE cihe_sub.id_item = i.id_item AND cihe_sub.tipo_homologacao = 'Engenharia'
                ORDER BY cihe_sub.criado_em DESC LIMIT 1) as homolog_engenharia_email,
                COALESCE(nve_attr_counts.count_cd_atributo_ncm, 0) AS has_nve, -- *** MODIFICAÇÃO 1 (uso) ***
                lessin.*",
                false
            );

            if ($hasOwner) {
                // Subconsultas de owner_usuario serão tratadas separadamente se necessário.
                // Por ora, mantendo o foco nas 3 principais.
                $this->db->select("
                (SELECT GROUP_CONCAT(DISTINCT rg.nome SEPARATOR ', ')
                FROM owner_usuario ou
                JOIN usuario rg ON ou.id_usuario = rg.id_usuario
                WHERE ou.id_owner = o.id_owner AND ou.responsavel_gestor = 1) AS responsaveis_gestores_nomes,
                (SELECT GROUP_CONCAT(DISTINCT rg.email SEPARATOR ', ')
                FROM owner_usuario ou
                JOIN usuario rg ON ou.id_usuario = rg.id_usuario
                WHERE ou.id_owner = o.id_owner AND ou.responsavel_gestor = 1) AS responsaveis_gestores_emails", false);
            }
        } elseif (!$useViewComex && $this->db->table_exists('comex')) {
            $this->db->select(
                "
                gt.id_grupo_tarifario,
                COUNT(DISTINCT CASE WHEN ex.cod_tipo_ex < 5 THEN ex.num_ex END) AS total_ex,
                COUNT(DISTINCT CASE WHEN ex.cod_tipo_ex > 5 THEN ex.num_ex END) AS total_exipi,
                gt.ncm_recomendada,
                i.id_item,
                i.part_number,
                i.estabelecimento,
                i.descricao_mercado_local,
                i.id_grupo_tarifario,
                gt.descricao as grupo_tarifario,
                i.caracteristicas,
                i.subsidio,
                COALESCE(ruf.email, 'Usuário não encontrado') as resp_usuario_fiscal,
                COALESCE(rue.email, 'Usuário não encontrado') as resp_usuario_engenharia,
                i.memoria_classificacao,
                item.evento,
                i.num_ex_ii,
                i.num_ex_ipi,
                wf.status as status_atributos,
                item.status_attr as status_preenchimento,
                i.dispositivo_legal,
                i.solucao_consulta,
                i.funcao,
                i.inf_adicionais,
                i.aplicacao,
                i.marca,
                i.suframa_destaque,
                i.suframa_ppb,
                i.suframa_descricao,
                i.suframa_produto,
                i.suframa_codigo,
                i.li_orgao_anuente,
                i.li,
                i.antidumping,
                i.li_destaque,
                item.descricao_proposta_completa,
                i.material_constitutivo,
                i.cod_cest,
                item.ncm as ncm_atual,
                i.ncm_proposto,
                item.status_attr,
                item.ncm_fornecedor,
                item.descricao       as descricao_atual,
                item.peso,
                item.prioridade,
                item.lista_cliente,
                item.lista_becomex,
                item.regra_aplicada,
                item.item_ja_homologado,
                item.observacoes,
                item.cod_owner,
                item.descricao_global,
                item.dat_criacao,
                item.data_modificacao,
                item.integracao_novo_material,
                ep.nome as empresa_prioridade,
                comex.ind_ecomex as indicador_ecomex,
                comex.num_di as num_di,
                comex.data_di as data_di,
                comex.ind_drawback as ind_drawback,
                comex.ncm as ncm_ecomex,
                v_item_status.status_formatado as item_status,
                -- Subconsultas de homologação serão tratadas separadamente para melhor performance.
                -- Os joins foram removidos.
                (SELECT MAX(cihf_sub.criado_em)
                FROM cad_item_homologacao cihf_sub
                WHERE cihf_sub.id_item = i.id_item AND cihf_sub.tipo_homologacao = 'Fiscal' AND cihf_sub.homologado = 1) as homolog_fiscal_data,
                (SELECT MAX(cihe_sub.criado_em)
                FROM cad_item_homologacao cihe_sub
                WHERE cihe_sub.id_item = i.id_item AND cihe_sub.tipo_homologacao = 'Engenharia' AND cihe_sub.homologado = 1) as homolog_engenharia_data,
                (SELECT cihf_sub.homologado
                FROM cad_item_homologacao cihf_sub
                WHERE cihf_sub.id_item = i.id_item AND cihf_sub.tipo_homologacao = 'Fiscal'
                ORDER BY cihf_sub.criado_em DESC LIMIT 1) as homolog_fiscal_status,
                (SELECT cihe_sub.homologado
                FROM cad_item_homologacao cihe_sub
                WHERE cihe_sub.id_item = i.id_item AND cihe_sub.tipo_homologacao = 'Engenharia'
                ORDER BY cihe_sub.criado_em DESC LIMIT 1) as homolog_engenharia_status,
                (SELECT cihf_sub.motivo
                FROM cad_item_homologacao cihf_sub
                WHERE cihf_sub.id_item = i.id_item AND cihf_sub.tipo_homologacao = 'Fiscal'
                ORDER BY cihf_sub.criado_em DESC LIMIT 1) as homolog_fiscal_motivo,
                (SELECT cihe_sub.motivo
                FROM cad_item_homologacao cihe_sub
                WHERE cihe_sub.id_item = i.id_item AND cihe_sub.tipo_homologacao = 'Engenharia'
                ORDER BY cihe_sub.criado_em DESC LIMIT 1) as homolog_engenharia_motivo,
                (SELECT huf_sub.email
                FROM cad_item_homologacao cihf_sub
                JOIN usuario huf_sub ON huf_sub.id_usuario = cihf_sub.id_usuario
                WHERE cihf_sub.id_item = i.id_item AND cihf_sub.tipo_homologacao = 'Fiscal'
                ORDER BY cihf_sub.criado_em DESC LIMIT 1) as homolog_fiscal_email,
                (SELECT hue_sub.email
                FROM cad_item_homologacao cihe_sub
                JOIN usuario hue_sub ON hue_sub.id_usuario = cihe_sub.id_usuario
                WHERE cihe_sub.id_item = i.id_item AND cihe_sub.tipo_homologacao = 'Engenharia'
                ORDER BY cihe_sub.criado_em DESC LIMIT 1) as homolog_engenharia_email,
                COALESCE(nve_attr_counts.count_cd_atributo_ncm, 0) AS has_nve, -- *** MODIFICAÇÃO 1 (uso) ***
                lessin.*",
                false
            );
            if ($hasOwner) {
                // Subconsultas de owner_usuario serão tratadas separadamente se necessário.
                $this->db->select("
                (SELECT GROUP_CONCAT(DISTINCT rg.nome SEPARATOR ', ')
                FROM owner_usuario ou
                JOIN usuario rg ON ou.id_usuario = rg.id_usuario
                WHERE ou.id_owner = o.id_owner AND ou.responsavel_gestor = 1) AS responsaveis_gestores_nomes,
                (SELECT GROUP_CONCAT(DISTINCT rg.email SEPARATOR ', ')
                FROM owner_usuario ou
                JOIN usuario rg ON ou.id_usuario = rg.id_usuario
                WHERE ou.id_owner = o.id_owner AND ou.responsavel_gestor = 1) AS responsaveis_gestores_emails", false);
            }
        } else { // Ni view comex, ni table comex
            $this->db->select(
                "
                gt.id_grupo_tarifario,
                COUNT(DISTINCT CASE WHEN ex.cod_tipo_ex < 5 THEN ex.num_ex END) AS total_ex,
                COUNT(DISTINCT CASE WHEN ex.cod_tipo_ex > 5 THEN ex.num_ex END) AS total_exipi,
                gt.ncm_recomendada,
                i.id_item,
                i.part_number,
                i.estabelecimento,
                i.descricao_mercado_local,
                i.id_grupo_tarifario,
                gt.descricao as grupo_tarifario,
                i.caracteristicas,
                i.subsidio,
                COALESCE(ruf.email, 'Usuário não encontrado') as resp_usuario_fiscal,
                COALESCE(rue.email, 'Usuário não encontrado') as resp_usuario_engenharia,
                i.memoria_classificacao,
                item.evento,
                i.num_ex_ii,
                i.num_ex_ipi,
                wf.status as status_atributos,
                item.status_attr as status_preenchimento,
                i.dispositivo_legal,
                i.solucao_consulta,
                i.funcao,
                i.inf_adicionais,
                i.aplicacao,
                i.marca,
                i.suframa_destaque,
                i.suframa_ppb,
                i.suframa_descricao,
                i.suframa_produto,
                i.suframa_codigo,
                i.li_orgao_anuente,
                i.li,
                i.antidumping,
                i.li_destaque,
                item.descricao_proposta_completa,
                i.material_constitutivo,
                i.cod_cest,
                item.ncm as ncm_atual,
                i.ncm_proposto,
                item.status_attr,
                item.ncm_fornecedor,
                item.descricao       as descricao_atual,
                item.peso,
                item.prioridade,
                item.lista_cliente,
                item.lista_becomex,
                item.regra_aplicada,
                item.item_ja_homologado,
                item.observacoes,
                item.cod_owner,
                item.descricao_global,
                item.integracao_novo_material,
                ep.nome as empresa_prioridade,
                item.dat_criacao,
                item.data_modificacao,
                -- Removendo subconsultas de owner_codigo e owner_descricao daqui.
                -- Elas serão adicionadas condicionalmente se $hasOwner for true, usando o JOIN 'o'.
                -- (SELECT o.codigo FROM `owner` o WHERE `item`.`cod_owner` = `o`.`codigo` LIMIT 1) AS owner_codigo, -- REMOVIDO
                -- (SELECT o.descricao FROM `owner` o WHERE `item`.`cod_owner` = `o`.`codigo` LIMIT 1) AS owner_descricao, -- REMOVIDO (assumindo que era FROM owner o)
                v_item_status.status_formatado as item_status,
                -- Subconsultas de homologação serão tratadas separadamente para melhor performance.
                -- Os joins foram removidos.
                (SELECT MAX(cihf_sub.criado_em)
                FROM cad_item_homologacao cihf_sub
                WHERE cihf_sub.id_item = i.id_item AND cihf_sub.tipo_homologacao = 'Fiscal' AND cihf_sub.homologado = 1) as homolog_fiscal_data,
                (SELECT MAX(cihe_sub.criado_em)
                FROM cad_item_homologacao cihe_sub
                WHERE cihe_sub.id_item = i.id_item AND cihe_sub.tipo_homologacao = 'Engenharia' AND cihe_sub.homologado = 1) as homolog_engenharia_data,
                (SELECT cihf_sub.homologado
                FROM cad_item_homologacao cihf_sub
                WHERE cihf_sub.id_item = i.id_item AND cihf_sub.tipo_homologacao = 'Fiscal'
                ORDER BY cihf_sub.criado_em DESC LIMIT 1) as homolog_fiscal_status,
                (SELECT cihe_sub.homologado
                FROM cad_item_homologacao cihe_sub
                WHERE cihe_sub.id_item = i.id_item AND cihe_sub.tipo_homologacao = 'Engenharia'
                ORDER BY cihe_sub.criado_em DESC LIMIT 1) as homolog_engenharia_status,
                (SELECT cihf_sub.motivo
                FROM cad_item_homologacao cihf_sub
                WHERE cihf_sub.id_item = i.id_item AND cihf_sub.tipo_homologacao = 'Fiscal'
                ORDER BY cihf_sub.criado_em DESC LIMIT 1) as homolog_fiscal_motivo,
                (SELECT cihe_sub.motivo
                FROM cad_item_homologacao cihe_sub
                WHERE cihe_sub.id_item = i.id_item AND cihe_sub.tipo_homologacao = 'Engenharia'
                ORDER BY cihe_sub.criado_em DESC LIMIT 1) as homolog_engenharia_motivo,
                (SELECT huf_sub.email
                FROM cad_item_homologacao cihf_sub
                JOIN usuario huf_sub ON huf_sub.id_usuario = cihf_sub.id_usuario
                WHERE cihf_sub.id_item = i.id_item AND cihf_sub.tipo_homologacao = 'Fiscal'
                ORDER BY cihf_sub.criado_em DESC LIMIT 1) as homolog_fiscal_email,
                (SELECT hue_sub.email
                FROM cad_item_homologacao cihe_sub
                JOIN usuario hue_sub ON hue_sub.id_usuario = cihe_sub.id_usuario
                WHERE cihe_sub.id_item = i.id_item AND cihe_sub.tipo_homologacao = 'Engenharia'
                ORDER BY cihe_sub.criado_em DESC LIMIT 1) as homolog_engenharia_email,
                COALESCE(nve_attr_counts.count_cd_atributo_ncm, 0) AS has_nve, -- *** MODIFICAÇÃO 1 (uso) ***
                lessin.*",
                false
            );
            if ($hasOwner) {
                // Subconsultas de owner_usuario serão tratadas separadamente se necessário.
                $this->db->select("
                (SELECT GROUP_CONCAT(DISTINCT rg.nome SEPARATOR ', ')
                FROM owner_usuario ou
                JOIN usuario rg ON ou.id_usuario = rg.id_usuario
                WHERE ou.id_owner = o.id_owner AND ou.responsavel_gestor = 1) AS responsaveis_gestores_nomes,
                (SELECT GROUP_CONCAT(DISTINCT rg.email SEPARATOR ', ')
                FROM owner_usuario ou
                JOIN usuario rg ON ou.id_usuario = rg.id_usuario
                WHERE ou.id_owner = o.id_owner AND ou.responsavel_gestor = 1) AS responsaveis_gestores_emails", false);
            }
        }

        if ($hasOwner) {
            // Este select adiciona owner_codigo e owner_descricao se $hasOwner
            // o que é bom, pois usa o JOIN 'o' que será adicionado abaixo.
            $this->db->select("
            o.codigo as owner_codigo,
            o.descricao as owner_descricao,", false);
        }

        if ($customer_can_nve) {
            $this->db->select(
                "
                ctrl_ex_ipi.num_ex AS num_ex_ipi,
                ctrl_ex_ipi.descricao_linha1 AS descricao_ex_linha1_ipi,
                ctrl_ex_ii.num_ex AS num_ex_ii,
                ctrl_ex_ii.descricao_linha1 AS descricao_ex_linha1_ii,
                GROUP_CONCAT(
                    DISTINCT CONCAT(
                        cnve.nve_atributo, ':', cnve.nve_valor
                    )
                    ORDER BY cnve.nve_atributo
                    SEPARATOR '||'
                ) AS nve_full_info",
                false
            );

            $this->db->join('cad_item_nve cnve', 'cnve.id_item = i.id_item', 'left');

            $this->db->join('tec_ncm_ex_tarif ctrl_ex_ii', 'ctrl_ex_ii.num_ex = i.num_ex_ii AND i.ncm_proposto = ctrl_ex_ii.cod_ncm', 'left');
            $this->db->join('tec_ncm_ex_tarif ctrl_ex_ipi', 'ctrl_ex_ipi.num_ex = i.num_ex_ipi AND i.ncm_proposto = ctrl_ex_ipi.cod_ncm', 'left');
        }

        // JOIN com vw_comex ou comex
        if ($useViewComex && $this->db->table_exists('vw_comex')) {
            // Ajuste: Adicionar vw_comex.unidade_negocio se for relevante como em 'comex'
            // A condição de JOIN original para 'comex' incluía 'unidade_negocio = i.estabelecimento'
            // Verifique se vw_comex também precisa disso ou se a estrutura é diferente.
            // Assumindo que o join original para vw_comex era o intencionado:
            $this->db->join('vw_comex', 'vw_comex.part_number_original = i.part_number AND vw_comex.id_empresa = i.id_empresa', 'left');
        } elseif (!$useViewComex && $this->db->table_exists('comex')) {
            $this->db->join('comex FORCE INDEX (index_helper)', 'comex.part_number_original = i.part_number AND comex.id_empresa = i.id_empresa AND comex.unidade_negocio = i.estabelecimento', 'left');
        }

        // v_item_status
        $this->db->join('v_item_status', 'v_item_status.id_empresa = i.id_empresa and v_item_status.part_number = i.part_number and v_item_status.estabelecimento   = i.estabelecimento', 'inner');

        // responsáveis
        $this->db->join('usuario ruf', 'ruf.id_usuario = i.id_resp_fiscal',     'left');
        $this->db->join('usuario rue', 'rue.id_usuario = i.id_resp_engenharia', 'left');

        $this->db->join('status_wf_atributos wf', 'wf.id = item.wf_status_atributos', 'left');


        // JOIN TABELA OWNER
        if ($hasOwner) {
            $this->db->join('owner o', "item.cod_owner = o.codigo AND (o.cnpj_raiz = '{$cnpj_raiz_logado}' OR o.cnpj_raiz IS NULL) ", 'left');
            // Os JOINS para owner_usuario e usuario rg para os GROUP_CONCAT de gestores
            // precisariam de uma subconsulta agregada para evitar multiplicação de linhas se fossem
            // usados para selecionar campos individuais E para os GROUP_CONCATs.
            // Se o único propósito deles aqui é para as subconsultas SELECT de gestores,
            // essa abordagem precisará ser refeita com um JOIN de subconsulta agregada.
            // $this->db->join('owner_usuario ou', 'o.id_owner = ou.id_owner AND ou.responsavel_gestor = 1', 'left');
            // $this->db->join('usuario rg', 'ou.id_usuario = rg.id_usuario', 'left');
        }

        // responsáveis homologação
        // $this->db->join('cad_item_homologacao cihf', 'cihf.id_item = i.id_item and cihf.tipo_homologacao = "Fiscal"', 'left');
        // $this->db->join('usuario huf', 'huf.id_usuario = cihf.id_usuario', 'left');

        // $this->db->join('cad_item_homologacao cihe', 'cihe.id_item = i.id_item and cihe.tipo_homologacao = "Engenharia"', 'left');
        // $this->db->join('usuario hue', 'hue.id_usuario = cihe.id_usuario', 'left');

        $this->db->join('lessin', 'item.id_lessin = lessin.id', 'left');
        $this->db->join('grupo_tarifario gt', 'gt.id_grupo_tarifario = i.id_grupo_tarifario', 'left'); // Um único JOIN para gt é suficiente. Removi o alias 'g'
        $this->db->join('tec_ncm_ex_tarif ex', 'ex.cod_ncm = gt.ncm_recomendada AND ex.num_ex IS NOT NULL AND ex.dat_vigencia_fim >= CURDATE()', 'left');

        $this->db->join('empresa_prioridades ep', 'ep.id_prioridade = item.id_prioridade', 'left');

        if (!empty($idItens)) {
            $this->db->where_in('i.id_item', $idItens);
        }
        if ($this->db->table_exists('comex')) {
            if ($data_ini_imp = $this->get_state('filter.data_inicio_importado_modal')) {
                $data_ini_imp = DateTime::createFromFormat('d/m/Y', $data_ini_imp)->format('Y-m-d');
                $this->db->where('DATE(comex.data_criacao) >=', $data_ini_imp);
                $this->db->where('comex.ind_ecomex', 'EI');
            }

            if ($data_fim_imp = $this->get_state('filter.data_fim_importado_modal')) {
                $data_fim_imp = DateTime::createFromFormat('d/m/Y', $data_fim_imp)->format('Y-m-d');
                $this->db->where('DATE(comex.data_criacao) <=', $data_fim_imp);
                $this->db->where('comex.ind_ecomex', 'EI');
            }
        }
        // As colunas do GROUP BY devem incluir todas as colunas não agregadas no SELECT
        // ou o MySQL deve estar configurado para permitir isso (ONLY_FULL_GROUP_BY desabilitado).
        // Para maior robustez e para que os GROUP_CONCAT(cnve...) funcionem corretamente
        // se houver múltiplos 'cnve' por 'i.id_item', o i.id_item (ou colunas que o determinem unicamente)
        // devem estar no group by.
        // A consulta original agrupava por i.part_number, i.estabelecimento.
        // Para que GROUP_CONCAT(cnve.nve_atributo) funcione corretamente com o join 'cnve',
        // i.id_item (que é a chave de junção para cnve) precisa ser único por grupo
        // (part_number, estabelecimento) ou ser incluído no GROUP BY.
        // Vamos assumir que i.id_item é único por (part_number, estabelecimento, id_empresa)
        // Se não for, você precisará adicionar i.id_item ao GROUP BY e a todas as colunas
        // selecionadas que não são agregadas e não dependem funcionalmente do group by.
        // Por simplicidade, mantendo o GROUP BY original, mas isso é um ponto de atenção.
        $this->db->group_by('i.part_number, i.estabelecimento');


        $this->db->order_by('i.part_number', 'asc');

        // Debugar a consulta
        // log_message('error', $this->db->get_compiled_select('cad_item i'));
        // die($this->db->get_compiled_select('cad_item i'));

        return $this->db->get('cad_item i');
    }

    public function get_homolog_info($id_item)
    {
        $this->db->select('u.nome, ih.homologado, ih.tipo_homologacao, ih.criado_em');
        $this->db->where('ih.id_item', $id_item);
        $this->db->join('usuario u', 'u.id_usuario = ih.id_usuario', 'inner');
        $query = $this->db->get('cad_item_homologacao ih');
        return $query->result();
    }

    public function check_item_homologado_by_tipo($id_item, $tipo_homologacao, $return_row = FALSE, $all = FALSE)
    {
        $this->db->where('ih.tipo_homologacao', $tipo_homologacao);
        $this->db->where('ih.id_item', $id_item);

        if (!$all) {
            $this->db->where('ih.homologado', 1);
        }

        $query = $this->db->get('cad_item_homologacao ih');
        if ($query->num_rows() > 0) {

            if ($return_row) {
                return $query->row();
            }

            return $query->result();
            // return TRUE;
        }

        return FALSE;
    }

    private function apply_rules_homologacao($dbdata, $homologacoes, $tipo_homologacao, $id_item, $where = NULL)
    {
        if (isset($homologacoes['homologacao_engenharia']) && isset($homologacoes['homologacao_fiscal'])) {
            return true;
        }

        if (isset($homologacoes['homologacao_engenharia'])) {
            $dbdata['tipo_homologacao'] = 'Fiscal';
        }

        if (isset($homologacoes['homologacao_fiscal'])) {
            $dbdata['tipo_homologacao'] = 'Engenharia';
        }

        $this->db->where('id_item', $id_item);
        $this->db->where('tipo_homologacao', $dbdata['tipo_homologacao']);

        $query = $this->db->get('cad_item_homologacao');
        if ($query->num_rows() > 0) {
            $where = array('id_item' => $id_item, 'tipo_homologacao' => $dbdata['tipo_homologacao']);
            $this->db->update('cad_item_homologacao', $dbdata, $where);

            $this->db->where('id_item', $id_item);
            $query = $this->db->get('cad_item_homologacao');
            if ($query->num_rows() == 1) {
                if ($dbdata['tipo_homologacao'] == 'Fiscal') {
                    $dbdata['tipo_homologacao'] = 'Engenharia';
                } else if ($dbdata['tipo_homologacao'] == 'Engenharia') {
                    $dbdata['tipo_homologacao'] = 'Fiscal';
                }

                $dbdata['id_item'] = $id_item;
                $this->db->insert('cad_item_homologacao', $dbdata);
            }
        } else {
            $dbdata['id_item'] = $id_item;
            $this->db->insert('cad_item_homologacao', $dbdata);
        }
    }

    public function set_item_homologado($id_item, $tipo_homologacao, $id_usuario, $is_homologado, $motivo, $criado_em = NULL, $homologacoes = NULL)
    {
        $item_status = $this->get_entry($id_item);

        $this->db->where('id_item', $id_item);
        $this->db->where('tipo_homologacao', $tipo_homologacao);
        $query = $this->db->get('cad_item_homologacao');

        $dbdata['homologado'] = $is_homologado;

        if (!$criado_em) {
            $dbdata['criado_em']  = date('Y-m-d H:i:s');
        } else {
            $dbdata['criado_em']  = $criado_em;
        }

        $dbdata['motivo']     = $motivo;
        $dbdata['id_usuario'] = $id_usuario;
        //NANA

        if ($query->num_rows() > 0) {
            $this->db->query("SET @disable_cad_item_homologacao_update_trigger = TRUE");
            $where = array('id_item' => $id_item, 'tipo_homologacao' => $tipo_homologacao);
            $this->db->update('cad_item_homologacao', $dbdata, $where);
            $this->apply_rules_homologacao($dbdata, $homologacoes, $tipo_homologacao, $id_item, $where);
            $this->db->query("SET @disable_cad_item_homologacao_update_trigger = NULL");
        } else {
            $dbdata['id_item'] = $id_item;
            $dbdata['tipo_homologacao'] = $tipo_homologacao;
            $this->db->insert('cad_item_homologacao', $dbdata);
            $this->apply_rules_homologacao($dbdata, $homologacoes, $tipo_homologacao, $id_item);
        }

        // Atualiza o campo status_homologacao do cad_item baseando nos registros de homologação
        $qq = $this->db->query("
        UPDATE cad_item ci
            LEFT JOIN cad_item_homologacao cih on
                cih.id_item = ci.id_item AND cih.`tipo_homologacao` = 'Engenharia'
            LEFT JOIN cad_item_homologacao cih2 on
                cih2.id_item = ci.id_item AND cih2.`tipo_homologacao` = 'Fiscal'
            SET
                ci.status_homologacao = (cih.homologado & cih2.homologado)
            WHERE ci.id_item = '{$id_item}';
        ");

        $item = $this->get_entry($id_item);

        $this->load->library("Item/Status");

        switch ($is_homologado) {
            case 0:
                $this->status->set_status("nao_homologado");
                break;
            case 1:
                $this->status->set_status("homologado");
                $this->status->update_item($item_status->part_number, $item_status->estabelecimento);
                break;
            case 2:
                $this->status->set_status("obsoleto");
                break;
        }

        if ($this->status->check_homologations($item_status->id_empresa, $item_status->part_number, $item_status->estabelecimento, $is_homologado)) {
            $this->status->update_item($item_status->part_number, $item_status->estabelecimento);
        }

        $this->db->where('id_item', $id_item);
        $query = $this->db->get('cad_item_homologacao');

        if ($query->num_rows() == 1) {
            if ($is_homologado == 0) {
                $this->status->set_status("nao_homologado");
            } else if ($is_homologado == 1) {
                if (company_can('homologacao_engenharia') && company_can('homologacao_fiscal')) {
                    $this->status->set_status("homologar");
                } else {
                    $this->status->set_status("homologado");
                }
            }

            $this->status->update_item($item_status->part_number, $item_status->estabelecimento);
        } else if ($query->num_rows() == 2) {

            foreach ($query->result() as $item_homologado) {
                if ($item_homologado->homologado == 0) {
                    $this->status->set_status("nao_homologado");
                    $this->status->update_item($item_status->part_number, $item_status->estabelecimento);
                }
            }
        }

        switch ($is_homologado) {
            case 0:
                $desc_homologacao = 'reprovado';
                break;

            case 1:
                $desc_homologacao = 'aprovado';
                break;

            case 2:
                $desc_homologacao = 'obsoleto';
                break;

            default:
                $desc_homologacao = 'desconhecido';
        }

        // Logs
        $dbdata = array(
            'part_number'       => $item->part_number,
            'estabelecimento'   => $item->estabelecimento,
            'tipo_homologacao'  => $tipo_homologacao,
            'id_usuario'        => $id_usuario,
            'titulo'            => $desc_homologacao,
            'motivo'            => $motivo,
            'criado_em'         => date("Y-m-d H:i:s"),
            'id_empresa'        => $item->id_empresa
        );

        $this->load->model("item_log_model");
        $this->item_log_model->save($dbdata);

        /* Integração Simplus
         * @who: Thaynã Moretti
         * @when: 2016-06-08
        */

        $empresa = $this->empresa_model->get_entry($item->id_empresa);

        if ($empresa->integracao_simplus == 1 && 1 === 2) {
            $this->load->model('ncm_model');

            /* Verifica se o item já possui duas aprovações
             * de papéis diferentes (fiscal & técnico)
            */
            $this->db->select('
                (cih1.homologado & cih2.homologado) as status_homologacao,
                (cih1.homologado = 2 || cih2.homologado = 2) as inativo
            ');

            $this->db->join('cad_item_homologacao cih2', 'cih1.id_item = cih2.id_item', 'inner');

            $this->db->where('cih1.tipo_homologacao <> cih2.tipo_homologacao');
            $this->db->where('cih1.id_item', $id_item);

            $this->db->group_by('cih1.id_item');

            $homolog = $this->db->get('cad_item_homologacao cih1');

            if ($homolog->num_rows() > 0) {
                /* Início da construção do objeto
                 * para envio via WS para Simplus
                 * ------------------------------
                 * cnpj_fabricante: string
                 * gtin: string
                 * ncm: string
                 * grupo_tarifario: string
                 * descricao_longa: string
                 * descricao_curta: string
                 * data_aprovacao: datetime
                 * responsavel_fiscal: string
                 * responsavel_tecnico: string
                 * subsidio: string
                 * memoria_classificacao: string
                 * caracteristica: string
                 * status_homologacao: string
                */

                $item_homologacao = $homolog->row();

                $status_homologacao = '';

                if ($item_homologacao->inativo == 1) {
                    $status_homologacao = 'inativo';
                } else {
                    $status_homologacao = ($item_homologacao->status_homologacao == 1) ? 'homologado' : 'reprovado';
                }

                $item = $this->get_entry($id_item);
                $ncm = $this->ncm_model->get_entry($item->ncm_proposto);

                /*
                 * Responsáveis pelo item
                */
                $resp_fiscal = null;
                $resp_tecnico = null;

                if ($item->id_resp_fiscal) {
                    $fiscal = $this->usuario_model->get_entry($item->id_resp_fiscal);
                    $resp_fiscal = $fiscal->nome;
                }

                if ($item->id_resp_engenharia) {
                    $tecnico = $this->usuario_model->get_entry($item->id_resp_engenharia);
                    $resp_tecnico = $tecnico->nome;
                }

                $ws_data = array(
                    'cnpj_fabricante' => $empresa->cnpj,
                    'gtin' => $item->part_number,
                    'ncm' => $item->ncm_proposto,
                    'grupo_tarifario' => $item->grupo_tarifario_desc,
                    'descricao_curta' => $item->descricao_mercado_local,
                    'descricao_longa' => $item->descricao_proposta_completa,
                    'data_aprovacao' => (string) round(microtime(true) * 1000),
                    'responsavel_fiscal' => $resp_fiscal,
                    'responsavel_tecnico' => $resp_tecnico,
                    'subsidio' => $item->subsidio,
                    'memoria_classificacao' => $item->memoria_classificacao,
                    'caracteristica' => $item->caracteristicas,
                    'status_homologacao' => $status_homologacao
                );

                $json_ws_data = json_encode($ws_data);

                $this->load->library('guzzle');

                $client = new GuzzleHttp\Client();
                $url = 'http://homolog.simplustec.com.br/becomex/saveFiscalClassification';

                /* Array as Text */
                ob_start();
                print_r($ws_data);
                $text_ws_data = ob_get_contents();
                ob_end_clean();
                //

                try {
                    $response = $client->request(
                        'POST',
                        $url,
                        [
                            'json'
                            => $ws_data
                        ]
                    );

                    $filename = APPPATH . '/logs/integracao-simplus.txt';
                } catch (GuzzleHttp\Exception\BadResponseException $e) {
                    $response = $e->getResponse();
                    $responseBodyAsString = $response->getBody()->getContents();
                    $filename = APPPATH . '/logs/error-integracao-simplus.txt';
                }

                $message = '--------------------------------------------------------------' . PHP_EOL;
                $message .= '[' . date('Y-m-d H:i:s') . '] - Integração com SimplusTEC' . PHP_EOL;

                $message .= 'Empresa: ' . $empresa->nome_fantasia . ' (' . $empresa->cnpj . ')' . PHP_EOL;
                $message .= 'Dados: ' . $text_ws_data . PHP_EOL;
                $message .= 'Saída JSON: ' . $json_ws_data . PHP_EOL;
                $message .= 'WS Status Code: ' . $response->getStatusCode() . PHP_EOL;
                $message .= $responseBodyAsString ? 'Retorno: ' . $responseBodyAsString . PHP_EOL : null;

                if ((file_exists($filename) && is_writable($filename)) || !file_exists($filename)) {
                    file_put_contents($filename, $message, FILE_APPEND);
                }
            }
        }

        return TRUE;
    }

    public function get_total_by_ncm($capitulo_ncm, $cenario = 'atual')
    {
        $field = $cenario == 'atual' ? 'ncm_atual' : 'ncm_proposto';

        $this->db->where('i.id_empresa', sess_user_company());

        // Booking (somente itens que já foram homologados)
        // $this->db->where('i.status_homologacao', 1);
        $this->db->where(
            "EXISTS (SELECT 1 FROM cad_item_homologacao cih WHERE cih.id_item = i.id_item AND homologado = 1 AND cih.tipo_homologacao = 'Fiscal') AND EXISTS (SELECT 1 FROM cad_item_homologacao cih WHERE cih.id_item = i.id_item AND homologado = 1 AND cih.tipo_homologacao = 'Engenharia')",
            '',
            FALSE
        );

        if ($cenario == 'atual') {
            $this->db->like('ncm', $capitulo_ncm, 'after');
            $this->db->join('item', 'item.part_number = i.part_number AND item.id_empresa = i.id_empresa', 'left');
            $total = $this->db->count_all_results('cad_item i');
        } else {
            $this->db->like('ncm_proposto', $capitulo_ncm, 'after');
            $total = $this->db->count_all_results('cad_item i');
        }

        return $total;
    }

    /**
     * Verifica se o motivo informado deve ser salvo no log.
     *
     * @param string|array $motivo
     * @return boolean
     */
    private function shouldSaveToLog($motivo)
    {
        // Se for array, sempre salvar (já que tem estrutura válida)
        if (is_array($motivo)) {
            return !empty($motivo['descricao']) || !empty($motivo['titulo']);
        }

        // Se for string, aplicar a regra das tags
        return $this->compareEmAndStrong($motivo);
    }

    /**
     * Compara se o conteúdo de <em> é diferente de <strong>.
     *
     * @param string $input
     * @return boolean
     */
    public function compareEmAndStrong($input)
    {
        if (!is_string($input)) {
            return true;
        }

        preg_match('/<em>(.*?)<\/em>/', $input, $emMatches);
        preg_match('/<strong>(.*?)<\/strong>/', $input, $strongMatches);

        if (isset($emMatches[1]) && isset($strongMatches[1])) {
            return $emMatches[1] !== $strongMatches[1];
        }

        return true;
    }

    private function generateUniqueKey($dbdata)
    {
        return md5(
            $dbdata['part_number'] . '|' .
                $dbdata['id_usuario'] . '|' .
                $dbdata['id_empresa'] . '|' .
                $dbdata['titulo'] . '|' .
                $dbdata['motivo'] . '|' .
                $dbdata['estabelecimento'] . '|' .
                $dbdata['criado_em']
        );
    }

    public function update_permissions($data)
    {
        if (isset($data['prioridade']) || isset($data['id_prioridade'])) {
            if (!customer_has_role('alterar_criticidade', sess_user_id())) {
                unset($data['prioridade']);
                unset($data['id_prioridade']);
            }
        }
        return $data;
    }


    public function update_item($part_number, $id_empresa, $data = array(), $motivo = FALSE, $estabelecimento = NULL)
    {
        $res =  [];
        if ($data && $data !== NULL) {

            $data = $this->update_permissions($data);

            $this->db->set($data);

            $this->db->where('part_number', $part_number);
            $this->db->where('id_empresa', $id_empresa);

            if (!empty($estabelecimento)) {
                $this->db->where('estabelecimento', $estabelecimento);
            }

            $res = $this->db->update('cad_item i');
        }

        if (!empty($motivo) && $this->shouldSaveToLog($motivo)) {

            $dbdata = array(
                'part_number'   => $part_number,
                'id_usuario'    => sess_user_id(),
                'id_empresa'    => $id_empresa,
                'criado_em'     => date("Y-m-d H:i:s"),
                'titulo'        => is_array($motivo) ? $motivo['titulo'] : 'atualizacao',
                'motivo'        => is_array($motivo) ? $motivo['descricao'] : $motivo,
                'estabelecimento' => !empty($estabelecimento) ? $estabelecimento : ''
            );

            $uniqueKey = $this->generateUniqueKey($dbdata);
            if (!in_array($uniqueKey, $this->recentEntries)) {
                $this->db->insert('item_log', $dbdata);
                $this->recentEntries[] = $uniqueKey;
            }
        }

        $this->db->select('status_exportacao');
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $query = $this->db->get('cad_item');
        $row   = $query->row();
        $status_export = $row->status_exportacao;

        if ($status_export == 1) {
            $dbdata = array(
                'status_exportacao' => 0
            );

            $motivo = "Alteração do Status de Exportação: <em>Exportado</em> &rarr; <strong>Pendente</strong>";

            $this->db->set($dbdata);
            $this->db->where('id_empresa', $id_empresa);
            $this->db->where('part_number', $part_number);

            $this->db->update('cad_item');

            $dblogdata = array(
                'part_number'       => $part_number,
                'id_empresa'        => $id_empresa,
                'id_usuario'        => sess_user_id(),
                'titulo'            => 'atualizacao',
                'motivo'            => $motivo,
                'criado_em'         => date("Y-m-d H:i:s"),
                'id_item'           => 0
            );

            if (!empty($estabelecimento)) {
                $dblogdata['estabelecimento'] = $estabelecimento;
            }

            $this->db->insert('item_log', $dblogdata);
        }

        return $res;
    }

    public function delete($part_number, $id_empresa, $estabelecimento)
    {
        if (is_array($part_number) && count($part_number) > 0) {
            $this->db->where_in('part_number', $part_number);
        } else {
            $this->db->where('part_number', $part_number);
        }

        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('estabelecimento', $estabelecimento);

        return $this->db->delete($this->_table);
    }

    public function delete_attr($id_item)
    {
        if (empty($id_item))
            return;

        $this->db->select('*');
        $this->db->from('cad_item_attr');
        $this->db->where('id_item', $id_item);
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            $rows = $query->result_array();

            // Remover o campo 'id' para permitir auto_increment na tabela de log
            foreach ($rows as &$row) {
                unset($row['id']);
            }

            $this->db->insert_batch('logs_delete_cad_item_attr', $rows);

            $this->db->where('id_item', $id_item);
            return $this->db->delete('cad_item_attr');
        }
    }

    public function atualiza_cad_item($part_number, $id_empresa, $dbdata, $estabelecimento = null)
    {
        $this->db->set($dbdata);
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);

        if (empty($estabelecimento)) {
            $this->db->where('estabelecimento IS NULL', NULL, TRUE);
        } else {
            $this->db->where('estabelecimento', $estabelecimento);
        }

        $this->db->update($this->_table);
    }

    public function update_ex_tarifarios_from_grupo($dbdata, $id_grupo_tarifario, $ncm_recomendada)
    {
        if (isset($dbdata['num_ex_ii'])) {
            $this->db->set('num_ex_ii', $dbdata['num_ex_ii']);
            $this->db->where('num_ex_ii IS NULL', NULL, TRUE);
        }

        if (isset($dbdata['num_ex_ipi'])) {
            $this->db->set('num_ex_ipi', $dbdata['num_ex_ipi']);
            $this->db->where('num_ex_ipi IS NULL', NULL, TRUE);
        }

        $this->db->where('id_grupo_tarifario', $id_grupo_tarifario);
        $this->db->where('ncm_proposto', $ncm_recomendada);

        $this->db->update($this->_table);
    }

    public function get_pendencias()
    {
        $id_empresa = sess_user_company();
        $query = $this->db->query("
            select  ii.pendencias_ex_ii,
                ii.itens_classif_ex_ii,
                ipi.pendencias_ex_ipi,
                ipi.itens_classif_ex_ipi,
                nve_valido.total - nve_atribuido.itens_classif_nve as pendencias_nve,
                nve_atribuido.itens_classif_nve,
                cest.*,
                attrs.*
            from
                (select     sum(case when nullif(c.num_ex_ii, '') is null then 1 else 0 end) as pendencias_ex_ii,
                            sum(case when nullif(c.num_ex_ii, '') is not null then 1 else 0 end) as itens_classif_ex_ii
                    from    cad_item c
                            inner join ncm_valido_ex_ii ex_ii
                                on c.ncm_proposto = ex_ii.cod_ncm
                    where   c.id_empresa = {$id_empresa}) ii,

                (select     sum(case when nullif(c.num_ex_ipi, '') is null then 1 else 0 end) as pendencias_ex_ipi,
                            sum(case when nullif(c.num_ex_ipi, '') is not null then 1 else 0 end) as itens_classif_ex_ipi
                    from    cad_item c
                            inner join ncm_valido_ex_ipi ex_ipi
                                on c.ncm_proposto = ex_ipi.cod_ncm
                    where   c.id_empresa = {$id_empresa}) ipi,

                (select     count(*) as total
                    from    cad_item c
                            inner join ncm_valido_nve nve
                                on c.ncm_proposto = nve.cod_ncm
                    where   c.id_empresa = {$id_empresa}) nve_valido,

                (select     count(distinct c.id_item) as itens_classif_nve
                    from    cad_item c
                            inner join cad_item_nve nve
                                on c.id_item = nve.id_item
                    where   c.id_empresa = {$id_empresa}) nve_atribuido,

                (select     sum(case when nullif(c.cod_cest, '') is null then 1 else 0 end) as pendencias_cest,
                            sum(case when nullif(c.cod_cest, '') is not null then 1 else 0 end) as itens_classif_cest
                    from    cad_item c
                    where   c.id_empresa = {$id_empresa}) cest,
                
                (select 
                    sum(case when nullif(ca.codigo, '') is null then 1 else 0 end) as pendencias_attrs,
                    sum(case when nullif(ca.codigo, '') is not null then 1 else 0 end) as classificados_attrs 
                    from cad_item c
                    inner join cad_item_attr ca on ca.id_item = c.id_item
                    where ca.obrigatorio = 1 and c.id_empresa = {$id_empresa}
                ) attrs;

        ");

        return $query->row();
    }

    public function check_item_exists($part_number, $id_empresa, $estabelecimento = NULL)
    {
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);

        if (!empty($estabelecimento)) {
            $this->db->where('estabelecimento', $estabelecimento);
        } else {
            $this->db->where('(estabelecimento IS NULL OR estabelecimento = "")', NULL, TRUE);
        }

        $query = $this->db->get($this->_table);

        return ($query->num_rows > 0) ? true : false;
    }

    public function atualiza_status_exportacao($status = 0, $id_itens = array(), $id_empresa, $part_number = 0, $estabelecimento = 0)
    {
        if (empty($id_itens) || $id_itens == NULL) return false;
        if ($part_number != 0) {
            $this->db->select('status_exportacao');
            $this->db->where('part_number', $part_number);
            $this->db->where('id_empresa', $id_empresa);
            $query = $this->db->get('cad_item');
            $row   = $query->row();
            $status_export = $row->status_exportacao;

            if ($status_export != $status) {
                if ($status == 0) {
                    $motivo = "Alteração do Status de Exportação: <em>Exportado</em> &rarr; <strong>Pendente</strong>";
                } elseif ($status == 1) {
                    $motivo = "Alteração do Status de Exportação: <em>Pendente</em> &rarr; <strong>Exportado</strong>";
                }

                $dblogdata = array(
                    'part_number'       => $part_number,
                    'id_empresa'        => $id_empresa,
                    'id_usuario'        => sess_user_id(),
                    'titulo'            => 'atualizacao',
                    'motivo'            => $motivo,
                    'criado_em'         => date("Y-m-d H:i:s"),
                );

                if (!empty($estabelecimento)) {
                    $dblogdata['estabelecimento'] = $estabelecimento;
                }

                $this->db->insert('item_log', $dblogdata);
            }
        }

        $this->db->where_in('id_item', $id_itens);
        $this->db->where('id_empresa', $id_empresa);

        return $this->db->update($this->_table, array('status_exportacao' => $status));
    }

    public function atualiza_status_exportacao_homologacao($id_empresa, $status = 0, $id_itens = array())
    {
        if (empty($id_itens) || $id_itens == NULL) return false;
        $res = [];
        foreach ($id_itens as $item) {
            $this->db->select('status_exportacao');
            $this->db->where('part_number', $item['part_number']);
            $this->db->where('id_empresa', $id_empresa);
            $query = $this->db->get('cad_item');
            $row   = $query->row();
            $status_export = $row->status_exportacao;

            if ($status_export != $status) {
                if ($status == 0) {
                    $motivo = "Alteração do Status de Exportação: <em>Exportado</em> &rarr; <strong>Pendente</strong>";
                } elseif ($status == 1) {
                    $motivo = "Alteração do Status de Exportação: <em>Pendente</em> &rarr; <strong>Exportado</strong>";
                }

                $dblogdata = array(
                    'part_number'       => $item['part_number'],
                    'id_empresa'        => $id_empresa,
                    'id_usuario'        => sess_user_id(),
                    'titulo'            => 'atualizacao',
                    'motivo'            => $motivo,
                    'criado_em'         => date("Y-m-d H:i:s"),
                );

                if (!empty($item['estabelecimento'])) {
                    $dblogdata['estabelecimento'] = $item['estabelecimento'];
                }

                $this->db->insert('item_log', $dblogdata);
            }

            $this->db->where('id_item', $item['id_item']);
            $this->db->where('id_empresa', $id_empresa);

            $res = $this->db->update($this->_table, array('status_exportacao' => $status));
        }

        return $res;
    }

    public function get_termo_pesquisa($part_number = '', $descricao = '', $id_empresa = '')
    {
        if ($id_empresa == '') {
            $id_empresa = sess_user_company();
        }

        $this->db->where('id_empresa', $id_empresa);

        if (!empty($part_number)) {
            $this->db->where('part_number', $part_number);
        }

        if (!empty($descricao)) {
            $this->db->where('descricao', $descricao);
        }

        $query = $this->db->get('item');

        $item = $query->row();

        if (empty($item)) {
            return '';
        }

        $termoPesquisa = '';

        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('checked', 1);
        $this->db->order_by('index', 'ASC');

        $query = $this->db->get('empresa_diana_inf');

        foreach ($query->result() as $attr) {
            $termoPesquisa .= $item->{$attr->slug} . ' ';
        }

        return trim($termoPesquisa);
    }

    public function getPacoteEventos($atribuidos = true, $filter_status = false)
    {
        $this->db->where('i.id_empresa', sess_user_company());
        $this->db->where('evento IS NOT NULL', null, false);
        $this->db->where('evento !=', '');

        $this->db->select('DISTINCT(evento)');

        $this->db->order_by('evento', 'asc');

        if (!$atribuidos) {
            $this->db->join('cad_item ci', 'ci.part_number = i.part_number and ci.estabelecimento = i.estabelecimento and i.id_empresa = ci.id_empresa', 'left');
            $this->db->where('ci.part_number is null', null, false);
        }

        /*
        *  Status de itens que são exibidos na tela de Dados Técnicos
        *  6 = Em Análise
        *  7 = Pendente de Informações
        * 8 = Perguntas Respondidas
        * 9 = Revisar Informações ERP
        * 11 = Revisar Informações Técnicas
        * 12 = Informações ERP Revisadas
        * 13 = Aguardando Definição Responsável
        * 14 = Aguardando Descrição
        * 15 = Perguntas Respondidas (Novas)
        */
        $status_atribuir_grupo = [6, 7, 8, 9, 11, 12, 13, 14, 15];
        if ($filter_status) {
            $this->db->where_in('i.id_status', $status_atribuir_grupo);
        }

        $query = $this->db->get('item i');

        return $query->num_rows() > 0 ? $query->result() : array();
    }

    public function getPacoteEventosByIdEmpresa($id_empresa = null)
    {

        if ($id_empresa == null) {
            $id_empresa = sess_user_company();
        }

        $this->db->where('i.id_empresa', $id_empresa);
        $this->db->where('evento IS NOT NULL', null, false);
        $this->db->where('evento !=', '');

        $this->db->select('DISTINCT(evento)');

        $this->db->order_by('evento', 'asc');


        $query = $this->db->get('item i');

        return $query->num_rows() > 0 ? $query->result() : array();
    }

    public function getEstabelecimentos()
    {
        $this->db->where('id_empresa', sess_user_company());
        $this->db->where('estabelecimento IS NOT NULL', null, false);
        $this->db->where('estabelecimento !=', '');

        $this->db->select('DISTINCT(estabelecimento)');

        $this->db->order_by('estabelecimento', 'asc');

        $query = $this->db->get('item');

        return $query->num_rows() > 0 ? $query->result() : array();
    }

    public function saveCadItem($data)
    {
        $this->db->where('part_number', $data['part_number']);
        $this->db->where('id_empresa', $data['id_empresa']);
        $this->db->where('estabelecimento', $data['estabelecimento']);
        $query = $this->db->get('cad_item');

        if ($query->num_rows() > 0) {
            $where = array('part_number' => $data['part_number']);
            $where['id_empresa'] = $data['id_empresa'];
            $where['estabelecimento'] = $data['estabelecimento'];
            $this->db->update('cad_item', $data, $where);
            return $query->row()->id_item;
        } else {
            $this->db->insert('cad_item', $data);
            return $this->db->insert_id();
        }
    }

    public function getPrioridades()
    {
        $this->db->where('id_empresa', sess_user_company());
        $this->db->select('DISTINCT(id_prioridade)');

        $this->db->order_by('id_prioridade', 'asc');

        $query = $this->db->get('item');

        return $query->num_rows() > 0 ? $query->result() : array();
    }

    public function getEntriesIntegracao($limit = NULL, $offset = NULL, $total = NULL, $ids = NULL)
    {
        $id_empresa = sess_user_company();

        if ($this->get_state('filter.id_empresa')) {
            $id_empresa = $this->get_state('filter.id_empresa');
        }

        $this->db->select('i.id_item, i.part_number, i.id_empresa, i.descricao_mercado_local, i.ncm_proposto, ih.criado_em as data_homologacao, ii.codigo_receita, ii.versao, a.modalidade, i.flag_integracao, i.erros_integracao');
        $this->db->join('item', 'i.part_number = item.part_number AND i.id_empresa = item.id_empresa AND i.estabelecimento = item.estabelecimento', 'inner');
        $this->db->join('cad_item_homologacao ih', 'ih.id_item = i.id_item', 'inner');
        $this->db->join('cad_item_integracao ii', 'ii.id_item = i.id_item', 'left');
        $this->db->join('cad_item_attr a', 'a.id_item = i.id_item', 'left');

        if (!empty($id_empresa)) {
            $this->db->where('item.id_empresa', $id_empresa);
        }

        if (!empty($ids)) {
            $this->db->where_in('i.id_item', $ids);
        }

        if ($pesquisar = $this->get_state('filter.pesquisar')) {
            $this->db->like('i.part_number', $pesquisar);
            $this->db->or_like('i.descricao_mercado_local', $pesquisar);
        }

        if ($ncm = $this->get_state('filter.ncm')) {
            $this->db->like('i.ncm_proposto', $ncm);
        }

        if ($codigo_receita = $this->get_state('filter.codigo_receita')) {
            $this->db->like('ii.codigo_receita', $codigo_receita);
        }

        if ($versao = $this->get_state('filter.versao')) {
            $this->db->like('ii.versao', $versao);
        }

        if ($modalidade = $this->get_state('filter.modalidade')) {
            $this->db->like('a.modalidade', $modalidade);
        }

        $this->db->order_by('i.part_number', 'ASC');

        $this->db->group_by('i.id_item');

        $query = $this->db->get($this->_table . ' i', $limit, $offset);

        if ($total) {
            return $query->num_rows();
        }

        return $query->result();
    }

    public function get_total_entriesV2($homologacoes = NULL, $owner_user = NULL, $criado_por_usuario_id = NULL)
    {
        $useViewComex = $this->config->item('use_view_comex');

        $this->filter_state(array(), TRUE, $homologacoes, false);

        if ($useViewComex && $this->db->table_exists('vw_comex')) {
            $this->db->join('vw_comex', 'vw_comex.part_number_original = i.part_number AND vw_comex.id_empresa = i.id_empresa', 'left');
        } else if (!$useViewComex && $this->db->table_exists('comex')) {
            $this->db->join('comex FORCE INDEX (index_helper)', 'comex.part_number_original = i.part_number AND comex.id_empresa = i.id_empresa AND comex.unidade_negocio = i.estabelecimento', 'left');
        }

        // $this->db->order_by('i.part_number', 'ASC');

        if ($this->get_state('filter.inner_with_logs')) {
            $this->db->join('item_log log', 'i.id_item = log.id_item', 'inner');
            // $this->db->group_by('i.id_item', TRUE);
        }

        if ($this->get_state('filter.group_similars')) {
            // $this->db->group_by('coalesce_part_number, item.estabelecimento', TRUE);
        }

        $owner_filter = $this->get_state('filter.owner');
        if ($owner_filter) {
            $this->db->where_in('item.cod_owner', $owner_filter);
        }

        $integracao = $this->get_state('filter.integracao');
        if ($integracao) {
            if ($integracao == 10) { // Pendente de integração
                $this->db->where('item.id_status', 10);
            } elseif ($integracao == 1001) { // Integrado
                $this->db->where('item.id_status !=', 10);
            }
        }

        $integracao_ecomex = $this->get_state('filter.integracao_ecomex');
        if ($integracao_ecomex == 10) { // Nacional
            if ($useViewComex) {
                $this->db->where("(vw_comex.ind_ecomex', 'EN' OR vw_comex.ind_ecomex = 'NE')", null, false);
            } else {
                $this->db->where("(comex.ind_ecomex = 'EN' OR comex.ind_ecomex = 'NE')", null, false);
            }
        } elseif ($integracao_ecomex == 1001) { // Importação
            if ($useViewComex) {
                $this->db->where('vw_comex.ind_ecomex', 'EI');
            } else {
                $this->db->where('comex.ind_ecomex', 'EI');
            }
        }

        if ($this->get_state('filter.evento')) {
            $evento = $this->get_state('filter.evento');

            if (!is_array($evento)) {
                $evento = array($evento);
            }

            $newEvento = array_filter($evento, function ($item) {
                return $item != "sem_evento";
            });

            $eventoConcatenado = "";

            foreach ($newEvento as $item) {
                if (empty($eventoConcatenado)) {
                    $eventoConcatenado .= "'{$item}'";
                } else {
                    $eventoConcatenado .= ",'{$item}'";
                }
            }

            if (!empty($newEvento)) {
                if (in_array("sem_evento", $evento)) {
                    $this->db->where("(item.evento IN ({$eventoConcatenado}) OR (item.evento = '' OR item.evento IS NULL))", null, false);
                } else {
                    $this->db->where("(item.evento IN ({$eventoConcatenado}))", null, false);
                }
            } elseif (in_array("sem_evento", $evento)) {
                $this->db->where("(item.evento = '' OR item.evento IS NULL)", NULL, FALSE);
            }
        }
        $this->db->from($this->_table . ' i ');
        $list_opt = $this->get_state('filter.list_opt');

        $hasSubquery = false;
        if (!empty($list_opt) && $list_opt != "todos") {
        } else {
            $hasSubquery = true;
        }

        if (($this->get_state('filter.atribuido_para') && $this->get_state('filter.atribuido_para') != -1) || $hasSubquery) {
            // $this->db->select('distinct i.id_item as qtd', false);

            $sqlBase = $this->db->get_compiled_select();

            $query = $this->db->query('SELECT 
                                        COUNT(*) AS qtd
                                    FROM (' . $sqlBase . ') as t1');
        } else {
            $this->db->select('count(*) as qtd');
            $query = $this->db->get();
        }

        if ($query->num_rows() > 0)
            return (int) $query->row()->qtd;

        return 0;
    }

    public function get_entriesV2($limit = null, $offset = null, $homologacoes = NULL, $compiled = false, $num_rows = false)
    {
        $this->load->model("empresa_model");
        $funcoes_adicionais = $this->empresa_model->get_funcoes_adicionais(null, sess_user_company());

        $useViewComex = $this->config->item('use_view_comex');

        $this->filter_state(array(), TRUE, $homologacoes, false);

        $select  = '';
        $select .= "
            distinct i.part_number,
            i.estabelecimento,
            item.part_number as part_number_item,
            item.estabelecimento as estabelecimento_item,
            i.id_item,
            i.id_empresa,
            i.descricao_mercado_local,
            i.ncm_proposto,
            i.status_simplus,
            i.status_exportacao,
            item.pn_primario_mpn,
            item.pn_secundario_ipn,
            item.prioridade,
            item.descricao as descricao_atual,
            item.ncm as ncm_atual,
            item.id_status,
            item.item_ja_homologado,
            item.cod_owner,
            item.evento as evento,
            item.dat_criacao,
            item.integracao_novo_material,
            item.data_modificacao,
            item.is_drawback,
            wf_status.status as wf_status_atributos,
            wf_status.color as wf_color,
            wf_status.id as wf_id,
            item.sistema_origem,
            s.slug,
            ep.nome as empresa_prioridade,
        ";

        if ($useViewComex && $this->db->table_exists('vw_comex')) {
            $select .= ",  vw_comex.ind_ecomex as indicador_ecomexx,
            vw_comex.num_di as num_di,
            vw_comex.data_di as data_di,
            vw_comex.ind_drawback as ind_drawback,
            vw_comex.ncm as ncm_ecomex,
            ";
        } else if (!$useViewComex && $this->db->table_exists('comex')) {
            $select .= ",  comex.ind_ecomex as indicador_ecomexx,
            comex.num_di as num_di,
            comex.data_di as data_di,
            comex.ind_drawback as ind_drawback,
            comex.ncm as ncm_ecomex,
            ";
        }

        if ($useViewComex && $this->db->table_exists('vw_comex')) {
            $this->db->join('vw_comex', 'vw_comex.part_number_original = i.part_number AND vw_comex.id_empresa = i.id_empresa', 'left');
        } else if (!$useViewComex && $this->db->table_exists('comex')) {
            $this->db->join('comex FORCE INDEX (index_helper)', 'comex.part_number_original = i.part_number AND comex.id_empresa = i.id_empresa AND comex.unidade_negocio = i.estabelecimento', 'left');
        }

        if ($this->db->table_exists('comex')) {
            if ($data_ini_imp = $this->get_state('filter.data_inicio_importado_modal')) {
                $data_ini_imp = DateTime::createFromFormat('d/m/Y', $data_ini_imp)->format('Y-m-d');
                $this->db->where('DATE(comex.data_criacao) >=', $data_ini_imp);
                $this->db->where('comex.ind_ecomex', 'EI');
            }

            if ($data_fim_imp = $this->get_state('filter.data_fim_importado_modal')) {
                $data_fim_imp = DateTime::createFromFormat('d/m/Y', $data_fim_imp)->format('Y-m-d');
                $this->db->where('DATE(comex.data_criacao) <=', $data_fim_imp);
                $this->db->where('comex.ind_ecomex', 'EI');
            }
        }

        // $this->db->order_by('i.part_number', 'ASC');

        $this->db->join('empresa_prioridades ep', 'ep.id_prioridade = item.id_prioridade', 'left');
        $this->db->join('status_wf_atributos wf_status', 'wf_status.id = item.wf_status_atributos', 'left');

        if ($this->get_state('filter.inner_with_logs')) {
            $this->db->join('item_log log', 'i.id_item = log.id_item', 'inner');
            $select .= ", MAX(log.criado_em) as ultimo_log";
            $this->db->group_by('i.id_item', TRUE);
        }

        if ($this->get_state('filter.group_similars')) {
            $this->db->group_by('coalesce_part_number, item.estabelecimento', TRUE);
        }

        $owner_filter = $this->get_state('filter.owner');
        if ($owner_filter) {
            $this->db->where_in('item.cod_owner', $owner_filter);
        }

        $integracao = $this->get_state('filter.integracao');
        if ($integracao) {
            if ($integracao == 10) { // Pendente de integração
                $this->db->where('item.id_status', 10);
            } elseif ($integracao == 1001) { // Integrado
                $this->db->where('item.id_status !=', 10);
            }
        }

        if ($prioridade = $this->get_state("filter.prioridade")) {
            $this->db->where_in("item.id_prioridade", $prioridade);
        }

        if ($sistema_origem = $this->get_state("filter.sistema_origem")) {

            $this->db->where_in("item.sistema_origem", $sistema_origem);
        }

        $integracao_ecomex = $this->get_state('filter.integracao_ecomex');
        if ($integracao_ecomex == 10) { // Nacional

            if ($useViewComex) {
                $this->db->where("(vw_comex.ind_ecomex', 'EN' OR vw_comex.ind_ecomex = 'NE')", null, false);
            } else {
                $this->db->where("(comex.ind_ecomex = 'EN' OR comex.ind_ecomex = 'NE')", null, false);
            }
        } elseif ($integracao_ecomex == 1001) { // Importação

            if ($useViewComex) {
                $this->db->where('vw_comex.ind_ecomex', 'EI');
            } else {
                $this->db->where('comex.ind_ecomex', 'EI');
            }
        }

        if ($this->get_state('filter.evento')) {
            $evento = $this->get_state('filter.evento');

            if (!is_array($evento)) {
                $evento = array($evento);
            }

            $newEvento = array_filter($evento, function ($item) {
                return $item != "sem_evento";
            });

            $eventoConcatenado = "";

            foreach ($newEvento as $item) {
                if (empty($eventoConcatenado)) {
                    $eventoConcatenado .= "'{$item}'";
                } else {
                    $eventoConcatenado .= ",'{$item}'";
                }
            }

            if (!empty($newEvento)) {
                if (in_array("sem_evento", $evento)) {
                    $this->db->where("(item.evento IN ({$eventoConcatenado}) OR (item.evento = '' OR item.evento IS NULL))", null, false);
                } else {
                    $this->db->where("(item.evento IN ({$eventoConcatenado}))", null, false);
                }
            } elseif (in_array("sem_evento", $evento)) {
                $this->db->where("(item.evento = '' OR item.evento IS NULL)", NULL, FALSE);
            }
        }

        $list_opt = $this->get_state('filter.list_opt');
        $hasGroupBy = false;
        if (!empty($list_opt) && $list_opt != "todos") {
        } else {
            $hasSubquery = true;
        }
        // if (($this->get_state('filter.atribuido_para') && $this->get_state('filter.atribuido_para') != -1) || $hasGroupBy) {
        //     $this->db->group_by('i.id_item');
        // }

        if ($this->get_state('filter.pendentes')) {
            if ($this->get_state('filter.pendentes') == 'pendente_atributos') {
                $select .= " 
                (SELECT count(*) FROM cad_item_attr attr where i.id_item  = attr.id_item 
                and attr.codigo is not null and attr.codigo <> '') as atributos_preenchidos,
                (SELECT count(*) FROM ncm_atributo ncm where i.ncm_proposto  = ncm.ncm  ) as atributos_a_preencher
                ";

                if (in_array("integracao_ecomex", $funcoes_adicionais)) {
                    $this->db->having("atributos_a_preencher > atributos_preenchidos AND (comex.ind_ecomex = 'EI') ");
                }
            }
        }

        $this->db->group_by('i.part_number, i.estabelecimento');
        $this->db->select($select, false);

        if (!$num_rows)
            $this->db->limit($limit, $offset);

        $this->db->from($this->_table . ' i');

        if ($compiled)
            return $this->db->limit($limit, $offset)->get_compiled_select();

        $query = $this->db->get();

        if ($num_rows)
            return  $query->num_rows();
        return $this->get_state("filter.use_unbuffered_query") ? $query : $query->result();
    }

    public function get_list_ex_ipi_by_empresa($id_empresa)
    {

        $current_date = date('Y-m-d');

        $sql = "SELECT
                    ci.num_ex_ipi,
                    ex.cod_ncm,
                    ex.dat_vigencia_ini,
                    (SELECT
                        descricao_linha1
                    FROM
                        tec_ncm_ex_tarif
                    WHERE
                        num_ex = ci.num_ex_ipi
                        AND cod_ncm = ci.ncm_proposto
                        AND dat_vigencia_ini = (
                            SELECT MAX(dat_vigencia_ini)
                            FROM tec_ncm_ex_tarif
                            WHERE num_ex = ci.num_ex_ipi
                            AND cod_ncm = ci.ncm_proposto
                            AND dat_vigencia_fim >= '{$current_date}'
                        )
                    LIMIT 1) AS titulo_ex
                FROM
                    cad_item ci
                LEFT JOIN tec_ncm_ex_tarif ex ON
                    ci.num_ex_ipi = ex.num_ex
                    AND ci.ncm_proposto = ex.cod_ncm
                    AND ex.dat_vigencia_ini = (
                        SELECT MAX(dat_vigencia_ini)
                        FROM tec_ncm_ex_tarif
                        WHERE num_ex = ci.num_ex_ipi
                        AND cod_ncm = ci.ncm_proposto
                        AND dat_vigencia_fim >= '{$current_date}'
                    )
                WHERE
                    ci.id_empresa = $id_empresa
                    AND ci.num_ex_ipi != '-1'
                    AND ex.cod_tipo_ex = 6
                    AND (ci.num_ex_ipi IS NOT NULL
                    AND TRIM(ci.num_ex_ipi) != '')
                GROUP BY
                    ci.num_ex_ipi,
                    ex.cod_ncm,
                    ex.dat_vigencia_ini
                ORDER BY
                    ci.num_ex_ipi ASC;";

        $query = $this->db->query($sql);

        return $query->num_rows() > 0 ? $query->result() : array();
    }

    public function get_list_ex_ii_by_empresa($id_empresa)
    {

        $current_date = date('Y-m-d');

        $sql = "SELECT
                    ci.num_ex_ii,
                    ex.cod_ncm,
                    ex.dat_vigencia_ini,
                    (SELECT
                        descricao_linha1
                    FROM
                        tec_ncm_ex_tarif
                    WHERE
                        num_ex = ci.num_ex_ii
                        AND cod_ncm = ci.ncm_proposto
                        AND dat_vigencia_ini = (
                            SELECT MAX(dat_vigencia_ini)
                            FROM tec_ncm_ex_tarif
                            WHERE num_ex = ci.num_ex_ii
                            AND cod_ncm = ci.ncm_proposto
                            AND dat_vigencia_fim >= '{$current_date}'
                        )
                    LIMIT 1) AS titulo_ex
                FROM
                    cad_item ci
                LEFT JOIN tec_ncm_ex_tarif ex ON
                    ci.num_ex_ii = ex.num_ex
                    AND ci.ncm_proposto = ex.cod_ncm
                    AND ex.dat_vigencia_ini = (
                        SELECT MAX(dat_vigencia_ini)
                        FROM tec_ncm_ex_tarif
                        WHERE num_ex = ci.num_ex_ii
                        AND cod_ncm = ci.ncm_proposto
                        AND dat_vigencia_fim >= '{$current_date}'
                    )
                WHERE
                    ci.id_empresa = $id_empresa
                    AND ci.num_ex_ii != '-1'
                    AND ex.cod_tipo_ex = 1
                    AND (ci.num_ex_ii IS NOT NULL
                    AND TRIM(ci.num_ex_ii) != '')
                GROUP BY
                    ci.num_ex_ii,
                    ex.cod_ncm,
                    ex.dat_vigencia_ini
                ORDER BY
                    ci.num_ex_ii ASC;";

        $query = $this->db->query($sql);

        return $query->num_rows() > 0 ? $query->result() : array();
    }

    public function get_list_ncm_proposto($id_empresa)
    {

        $data = array();

        $this->db->select('ci.ncm_proposto', FALSE);
        $this->db->where('ci.id_empresa', $id_empresa);
        $this->db->where("(ci.ncm_proposto IS NOT NULL AND TRIM(ci.ncm_proposto) != '') ");
        $this->db->group_by('ci.ncm_proposto', TRUE);
        $this->db->from($this->_table . ' ci');

        $query = $this->db->get();

        foreach ($query->result() as $row) {
            $data[] = $row->ncm_proposto;
        }

        return $data;
    }

    public function get_ex_ipi($id_empresa, $ncm_proposto, $num_ex_ipi)
    {

        $sql = "SELECT
                    ci.num_ex_ipi,
                    ex.cod_ncm,
                    (SELECT
                        descricao_linha1
                    FROM
                        tec_ncm_ex_tarif
                    WHERE
                        num_ex = ci.num_ex_ipi
                        AND cod_ncm = ci.ncm_proposto
                    LIMIT 1) AS titulo_ex
                FROM
                    cad_item ci
                LEFT JOIN tec_ncm_ex_tarif ex ON
                    ci.num_ex_ipi = ex.num_ex
                    AND ci.ncm_proposto = ex.cod_ncm
                WHERE
                    ci.id_empresa = $id_empresa
                    AND ex.cod_ncm = $ncm_proposto
                    AND ex.num_ex = $num_ex_ipi
                    AND ci.num_ex_ipi != '-1'
                    AND ex.cod_tipo_ex = 6
                    AND (ci.num_ex_ipi IS NOT NULL
                    AND TRIM(ci.num_ex_ipi) != '')
                GROUP BY
                    ci.num_ex_ipi,
                    ex.cod_ncm
                ORDER BY
                    ci.num_ex_ipi ASC
                LIMIT 1;";

        $query = $this->db->query($sql);

        return $query->num_rows() > 0 ? $query->result() : array();
    }

    public function remove_item_importado($part_number, $id_empresa, $estabelecimento = null)
    {
        if (empty($part_number) || empty($id_empresa))
            return;

        $this->db->where('part_number_original', $part_number);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('unidade_negocio', $estabelecimento);
        $result = $this->db->delete('comex');

        // Atualiza o status após remover da tabela comex
        if ($result) {
            $this->atualizar_status_atributos($part_number, $id_empresa, $estabelecimento);
        }
    }

    public function define_item_importado($part_number, $estabelecimento, $id_empresa)
    {
        $this->load->model("empresa_model");
        $this->load->model("comex_model");
        $dados_empresa = $this->empresa_model->get_entry($id_empresa);
        $check_data = $this->comex_model->check_imported($part_number, $id_empresa, $estabelecimento);

        if ($check_data)
            return;

        if (empty($dados_empresa))
            return;

        $cad_item = $this->get_entry_by_pn($part_number, $id_empresa, $estabelecimento);
        $ncm_definido = null;
        if (!empty($cad_item)) {
            $ncm_definido = $cad_item->ncm_proposto;
        }

        $cnpj_raiz = $this->getCnpjRaiz($dados_empresa->cnpj);

        $data = [
            'id_base_comex'           => '2',
            'id_empresa'              => $id_empresa,
            'part_number_original'    => $part_number,
            'part_number_modificado'  =>  $part_number,
            'cnpj_raiz'               => $cnpj_raiz,
            'unidade_negocio'         => $estabelecimento,
            'num_di'                  => null,
            'data_di'                 => null,
            'ind_ecomex'              => 'EI',
            'ind_drawback'            => 'N',
            'ncm'                     => $ncm_definido,
            'data_criacao'            => date('Y-m-d H:i:s'),
            'data_ultima_atualizacao' => date('Y-m-d H:i:s')


        ];

        $this->db->insert('comex', $data);

        // Atualiza o status após inserir na tabela comex, se tiver ncm definido
        if (!empty($ncm_definido)) {
            $this->atualizar_status_atributos($part_number, $id_empresa, $estabelecimento);
        }
    }

    /**
     * Atualiza o status de atributos do item baseado na condição se ele é importado ou não.
     *
     * @param string $part_number Part number do item
     * @param int $id_empresa ID da empresa do item
     * @param string $estabelecimento Estabelecimento do item
     * @return boolean True caso tenha atualizado o status com sucesso, False caso contrário.
     */
    public function atualizar_status_atributos($part_number, $id_empresa, $estabelecimento)
    {
        $this->load->model([
            'item_model',
            'comex_model',
            'log_wf_atributos_model'
        ]);

        $is_importado = $this->comex_model->check_imported($part_number, $id_empresa, $estabelecimento);

        $cad_item = $this->get_entry_by_pn($part_number, $id_empresa, $estabelecimento);
        $ncm_definido = null;
        if (!empty($cad_item)) {
            $ncm_definido = $cad_item->ncm_proposto;
        }

        $item = $this->item_model->get_entry_by_pn($part_number, $id_empresa, $estabelecimento);
        if (!$item) {
            return false;
        }

        if (!empty($ncm_definido)) {
            $this->atualizar_status_atributos_importados($item, $is_importado);
        }

        $id_item = $item->id_item ?? null;
        $justificativa = $is_importado ?
            'O item foi atualizado e teve seu status de atributos alterado para Análise de atributos - Fiscal' :
            'O item foi atualizado e teve seu status de atributos alterado para Item Nacional';

        $status_novo = $is_importado ? 2 : 1;
        $id_usuario = sess_user_id();

        // Só registra log se tiver com ncm definido
        if (!empty($ncm_definido)) {
            $this->log_wf_atributos_model->registrar_log(
                $id_item,
                $part_number,
                $estabelecimento,
                $id_empresa,
                $status_novo,
                'movimentacao manual',
                $id_usuario,
                $justificativa
            );
        }

        return true;
    }

    private function atualizar_status_atributos_importados($item, $is_importado)
    {
        $this->db->where('part_number', $item->part_number);
        $this->db->where('id_empresa', $item->id_empresa);
        $this->db->where('estabelecimento', $item->estabelecimento);
        $this->db->update('item', ['wf_status_atributos' => $is_importado ? 2 : 1]);
    }

    public function getCnpjRaiz($cnpj)
    {
        // Remove todos os caracteres que não são dígitos
        $cleanCnpj = preg_replace('/\D/', '', $cnpj);

        // Retorna os primeiros 8 dígitos do CNPJ
        return substr($cleanCnpj, 0, 8);
    }

    public function get_ex_ii($id_empresa, $ncm_proposto, $num_ex_ii)
    {

        $sql = "SELECT
                    ci.num_ex_ii,
                    ex.cod_ncm,
                    (SELECT
                        descricao_linha1
                    FROM
                        tec_ncm_ex_tarif
                    WHERE
                        num_ex = ci.num_ex_ii
                        AND cod_ncm = ci.ncm_proposto
                    LIMIT 1) AS titulo_ex
                FROM
                    cad_item ci
                LEFT JOIN tec_ncm_ex_tarif ex ON
                    ci.num_ex_ii = ex.num_ex
                    AND ci.ncm_proposto = ex.cod_ncm
                WHERE
                    ci.id_empresa = $id_empresa
                    AND ex.cod_ncm = $ncm_proposto
                    AND ex.num_ex = $num_ex_ii
                    AND ci.num_ex_ii != '-1'
                    AND ex.cod_tipo_ex = 1
                    AND (ci.num_ex_ii IS NOT NULL
                    AND TRIM(ci.num_ex_ii) != '')
                GROUP BY
                    ci.num_ex_ii,
                    ex.cod_ncm
                ORDER BY
                    ci.num_ex_ii ASC
                LIMIT 1;";

        $query = $this->db->query($sql);

        return $query->num_rows() > 0 ? $query->result() : array();
    }

    public function get_itens_to_export_diana($idItens)
    {
        $id_empresa = sess_user_company();
        $this->load->model('empresa_model');
        $empresa = $this->empresa_model->get_entry($id_empresa);
        $campos_adicionais = explode("|", $empresa->campos_adicionais);
        $hasOwner =  in_array('owner', $campos_adicionais);
        $list_opt = $this->get_state('filter.list_opt');

        if ($this->get_state('filter.list_opt')) {
            $this->db->where_in('slug', $list_opt);
            $query = $this->db->get('status');
            if ($query->num_rows() > 0) {
                $result = $query->result();
                $ids_status = array_map(function ($status) {
                    return $status->id;
                }, $result);

                $this->set_state('filter.list_opt_id', $ids_status);
            }
        }

        if (empty($idItens)) {
            $this->filter_state([], true, null, true, true);
        } else {
            if ($this->get_state('filter.use_index_helper') == true) {
                $this->db->join('item FORCE INDEX (helper)', 'i.part_number = item.part_number AND i.id_empresa = item.id_empresa AND i.estabelecimento = item.estabelecimento', 'inner');
            } else {
                $this->db->join('item', 'i.part_number = item.part_number AND i.id_empresa = item.id_empresa AND i.estabelecimento = item.estabelecimento', 'inner');
            }
        }

        $this->db->select(
            "i.id_item,
            i.part_number,
            i.estabelecimento,
            item.descricao_proposta_completa,
            i.ncm_proposto",
            false
        );

        if (!empty($idItens)) {
            $this->db->where_in('i.id_item', $idItens);
        }

        $this->db->group_by('i.part_number, i.estabelecimento');

        $this->db->order_by('i.part_number', 'asc');

        return $this->db->get('cad_item i');
    }

    public function get_all_ex_tarifarios($id_empresa, $ncm_propostos, $num_ex_ii_list, $num_ex_ipi_list)
    {
        $ncm_placeholders = implode(',', array_fill(0, count($ncm_propostos), '?'));

        $conditions = [];
        $params = $ncm_propostos;

        if (!empty($num_ex_ii_list)) {
            $ex_ii_placeholders = implode(',', array_fill(0, count($num_ex_ii_list), '?'));
            $conditions[] = "(ex.num_ex IN ($ex_ii_placeholders) AND ex.cod_tipo_ex = 1)";
            $params = array_merge($params, $num_ex_ii_list);
        }

        if (!empty($num_ex_ipi_list)) {
            $ex_ipi_placeholders = implode(',', array_fill(0, count($num_ex_ipi_list), '?'));
            $conditions[] = "(ex.num_ex IN ($ex_ipi_placeholders) AND ex.cod_tipo_ex = 6)";
            $params = array_merge($params, $num_ex_ipi_list);
        }

        $condition_sql = empty($conditions) ? "1=0" : implode(' OR ', $conditions);

        $sql = "SELECT
                    ex.cod_ncm,
                    ex.num_ex,
                    ex.descricao_linha1 AS titulo_ex,
                    ex.cod_tipo_ex
                FROM
                    tec_ncm_ex_tarif ex FORCE INDEX (idx_ncm_ex_tipo)
                WHERE
                    ex.cod_ncm IN ($ncm_placeholders)
                    AND ($condition_sql)
                    AND ex.cod_ncm IN (SELECT DISTINCT ncm_proposto FROM cad_item WHERE id_empresa = ?)";

        $params[] = $id_empresa;

        $query = $this->db->query($sql, $params);

        if ($query === false) {
            log_message('error', 'Erro na consulta SQL: ' . $this->db->error()['message']);
            return [];
        }

        $result = array();
        foreach ($query->result() as $row) {
            $key = $row->cod_ncm . '_' . trim($row->num_ex) . '_' . trim($row->cod_tipo_ex);
            $result[$key] = $row->titulo_ex;
        }

        return $result;
    }

    public function has_attr_cond($attr, $cond)
    {
        $cond_op  = $cond["condicao"]["operador"]; // O operador para comparação.
        $cond_val = $cond["condicao"]["valor"];    // O valor para ser comparado.
        $cond_res = FALSE;                         // O resultado final da comparação.

        if (!empty($attr['multivalorado'])) {
            $attr_vals = $attr["dbdata"]["codigo"]; // Multivalorado.

            if (!\is_array($attr_vals)) {
                $attr_vals = explode(",", $attr_vals);
            }

            foreach ($attr_vals as $attr_val) {

                $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");

                if ($cond_res) {
                    break;
                }
            }
        } else {
            $attr_val = $attr["dbdata"]["codigo"]; // Valor simples.

            if (strtoupper($attr["formaPreenchimento"]) == 'BOOLEANO' && $attr_val > 0) {
                $attr_val = "true";
            }

            $cond_res = eval("return \"$attr_val\" $cond_op \"$cond_val\";");
        }

        return $cond_res;
    }

    private function assoc_recursively($arr_dbdata, &$arr_attr, $parent_attr = NULL)
    {
        if (empty($arr_attr)) {
            return;
        }

        if (empty($arr_dbdata)) {
            $arr_dbdata = [];
        }

        foreach ($arr_attr as &$attr) {
            $attr_template = !empty($attr["atributo"]) ? $attr["atributo"] : $attr;

            $attr_template["dbdata"] = ["codigo" => ""];

            foreach ($arr_dbdata as $dbdata) {
                if (
                    !empty($parent_attr)
                    && !empty($dbdata["atributo_pai"])
                    && $dbdata["atributo_pai"] == $parent_attr["codigo"]
                    && $dbdata["atributo"] == $attr_template["codigo"]
                ) {
                    $attr_template["dbdata"] = $dbdata;
                } else if ($dbdata["atributo"] == $attr_template["codigo"]) {
                    $attr_template["dbdata"] = $dbdata;
                }
            }

            if ($attr_template["atributoCondicionante"] && !empty($attr_template["condicionados"])) {
                foreach ($attr_template["condicionados"] as &$cond) {

                    $index_atributo =  isset($attr_template["dbdata"]['atributo']) ?
                        $attr_template["dbdata"]['atributo'] : null;
                    $index_atributo_codigo =  isset($attr_template["dbdata"]['codigo']) ?
                        $attr_template["dbdata"]['codigo'] : null;

                    $index_atributo_pai =  isset($cond['atributo']['codigo']) ?
                        $cond['atributo']['codigo'] : null;
                    $index_atributo_pai_codigo =  isset($cond["condicao"]) ?
                        $cond["condicao"] : null;

                    $this->_attrs[] = [$index_atributo, $index_atributo_codigo, $index_atributo_pai, $index_atributo_pai_codigo];

                    if ($this->has_attr_cond($attr_template, $cond)) {
                        if (!empty($cond['atributo']["condicionados"])) {

                            $this->assoc_recursively($arr_dbdata, $cond['atributo']["condicionados"], $attr_template);
                        }

                        if (!empty($attr_template["dbdata"]['atributo'])) {
                            $this->_attrs['sim'][] = $attr_template["dbdata"]['atributo'];
                        }
                    }
                }

                if (!empty($this->_attrs['sim']) && in_array($attr_template['codigo'], $this->_attrs['sim'])) {
                    $this->assoc_recursively($arr_dbdata, $attr_template["condicionados"], $attr_template);
                }
            } else if (\strtoupper($attr_template["formaPreenchimento"]) == "COMPOSTO" && !empty($attr_template["listaSubatributos"])) {


                if (!empty($attr_template["listaSubatributos"])) {
                    foreach ($attr_template["listaSubatributos"] as $listaSub) {
                        $index_atributo =  isset($attr_template['codigo']) ?
                            $attr_template['codigo'] : null;

                        $index_atributo_codigo =  1;

                        $index_atributo_pai =  isset($listaSub['codigo']) ?
                            $listaSub['codigo'] : null;

                        $index_atributo_pai_codigo =  [
                            'operador' => '==',
                            'valor' => '1'
                        ];

                        $this->_attrs[] = [$index_atributo, $index_atributo_codigo, $index_atributo_pai, $index_atributo_pai_codigo];
                    }
                }

                $this->assoc_recursively($arr_dbdata, $attr_template["listaSubatributos"], $attr_template);
            }

            if (!empty($attr["atributo"])) {
                $attr["atributo"] = $attr_template;
            } else {
                $attr = $attr_template;
            }
        }
    }

    private function create_assoc_attrs_structure($ncm_item)
    {
        if (empty($ncm_item)) {
            return;
        }

        $ncm_item = \json_decode(\json_encode($ncm_item), TRUE);

        $arr_dbdata = $ncm_item["defaultAttrs"];
        $arr_attr   = $ncm_item["listaAtributos"];

        $this->assoc_recursively($arr_dbdata, $arr_attr);

        return $arr_attr;
    }

    private function filterUniqueAttributes($inputArray)
    {
        $attributeMap = [];

        foreach ($inputArray as $item) {
            $attribute = $item->atributo;
            $attributeMap[$attribute] = $item;
        }

        $resultArray = array_values($attributeMap);

        usort($resultArray, function ($a, $b) {
            return strtotime($b->atualizado_em) - strtotime($a->atualizado_em);
        });

        return $resultArray;
    }

    public function get_attr($id_item, $empty = FALSE)
    {
        if (empty($id_item)) {
            return FALSE;
        }

        if ($empty) {
            $this->db->where("codigo", "");
        }

        $this->db->where("id_item", $id_item);

        $query = $this->db->get('cad_item_attr');

        if (!$query->num_rows()) {
            return FALSE;
        }

        return $query->result();
    }

    private function get_required_attrs($listaAtributos)
    {
        if (empty($listaAtributos)) {
            return;
        }

        $listaAtributos = \json_decode(\json_encode($listaAtributos), TRUE); // Força a conversão para array.

        $ncm_item = [];
        $ncm_item['listaAtributos'] = $listaAtributos;

        \array_multisort($ncm_item["listaAtributos"]);

        return $ncm_item;
    }

    public function filtrarAtributosFilhos($data)
    {
        $result = [];

        foreach ($data as $item) {

            if (!is_array($item) || count($item) < 4) {
                continue;
            }

            list($atributoPai, $valorPai, $atributoFilho, $condicoes) = $item;

            if ($valorPai == '' || is_null($valorPai)) {
                continue;
            }

            if (!is_array($condicoes) || !isset($condicoes['operador'], $condicoes['valor'])) {
                continue;
            }

            if ($this->avaliarCondicao($valorPai, $condicoes)) {
                $result[] = $atributoFilho;
            }
        }

        return array_unique($result);
    }

    public function avaliarCondicao($valorPai, $condicao, $attr = null)
    {
        $operador = $condicao['operador'];
        $valorCondicao = $condicao['valor'];

        if ($condicao['valor'] == 'true') {
            $valorCondicao = '1';
        }

        if ($condicao['valor'] == 'false') {
            $valorCondicao = '0';
        }

        if (is_null($valorPai) || $valorPai == '') {
            return false;
        }

        switch ($operador) {
            case '==':
                $resultado = $valorPai == $valorCondicao;
                break;
            case '!=':
                $resultado = $valorPai != $valorCondicao;
                break;
            case '>':
                $resultado = $valorPai > $valorCondicao;
                break;
            case '<':
                $resultado = $valorPai < $valorCondicao;
                break;
            case '>=':
                $resultado = $valorPai >= $valorCondicao;
                break;
            case '<=':
                $resultado = $valorPai <= $valorCondicao;
                break;
            default:
                $resultado = false;
        }

        if (isset($condicao['composicao'], $condicao['condicao'])) {
            $composicao = $condicao['composicao'];
            $subCondicao = $condicao['condicao'];

            $subResultado = $this->avaliarCondicao($valorPai, $subCondicao);

            if ($composicao === '||') {
                $resultado = $resultado || $subResultado;
            } elseif ($composicao === '&&') {
                $resultado = $resultado && $subResultado;
            }
        }

        return $resultado;
    }

    public function get_grp_tarif_item($id_item)
    {
        $this->db->select('id_grupo_tarifario');
        $this->db->where('id_item', $id_item);
        return $this->db->get('cad_item')->row()->id_grupo_tarifario;
    }

    public function get_ncm_proposto($id_item)
    {
        if (is_Array($id_item)) {
            $itens = [];
            foreach ($id_item as $item) {
                if (!empty($item['id_item'])) {
                    $itens[] = $item['id_item'];
                } else if (!empty($item)) {
                    $itens[] = $item;
                }
            }
            if (!empty($itens)) {
                $this->db->where_in('id_item', $itens);
            } else {
                return;
            }
        } else {
            $this->db->where('id_item', $id_item);
        }

        $query = $this->db->get('cad_item');

        $ncm = $query->row();

        return $ncm->ncm_proposto;
    }

    public function limpar_registros_grupo_tarif_divergente($id_item, $grupo_tarifario_item)
    {
        // echo 'dd';
        // exit;
        if (empty($grupo_tarifario_item) || empty($id_item)) {
            return;
        }

        $this->db->select('*');
        $this->db->from('cad_item_attr');
        $this->db->where('id_item', $id_item);
        $this->db->where("id_grupo_tarifario <> '{$grupo_tarifario_item}'", NULL, FALSE);
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            $rows = $query->result_array();
            $rows_object = $query->result();

            foreach ($rows_object as $r) {
                // grupo tarifario que esta divergente na cad_item_attr em relacao a cad_item mas que é do mesmo id_item
                $this->db->where('id_grupo_tarifario', $r->id_grupo_tarifario);
                $query = $this->db->get('grupo_tarifario');
                $result_grupo_tarifario = $query->row();
                $ncm_grupo_tarifario = $result_grupo_tarifario->ncm_recomendada;

                // grupo_tarifario na cad_item
                $this->db->where('id_grupo_tarifario', $grupo_tarifario_item);
                $query = $this->db->get('grupo_tarifario');
                $result_grupo_tarifario = $query->row();
                $ncm_novo_grupo_tarifario = $result_grupo_tarifario->ncm_recomendada;

                if (!empty($ncm_grupo_tarifario) && !empty($ncm_novo_grupo_tarifario) && $ncm_grupo_tarifario != $ncm_novo_grupo_tarifario) {
                    $this->db->where('id_item', $id_item);
                    $this->db->where("id_grupo_tarifario <> '{$grupo_tarifario_item}'", NULL, FALSE);
                    $this->db->delete('cad_item_attr');

                    $logs_delete = [
                        'codigo' => $r->codigo,
                        'apresentacao' => $r->apresentacao,
                        'descricao' => $r->descricao,
                        'atributo_pai' => $r->atributo_pai,
                        'atributo' => $r->atributo,
                        'obrigatorio' => $r->obrigatorio,
                        'id_item' => $r->id_item,
                        'id_grupo_tarifario' => $r->id_grupo_tarifario,
                        'id_usuario' => $r->id_usuario,
                        'modalidade' => $r->modalidade,
                        'criado_em' => $r->criado_em,
                        'atualizado_em' => $r->atualizado_em,
                        'ativo' => $r->ativo,
                        'tenant_id' => $r->tenant_id
                    ];

                    $this->db->insert('logs_delete_cad_item_attr', $logs_delete);
                } else {
                    $this->db->set('id_grupo_tarifario', $grupo_tarifario_item);
                    $this->db->from('cad_item_attr');
                    $this->db->where('id_item', $id_item);
                    $this->db->where("id_grupo_tarifario <> '{$grupo_tarifario_item}'", NULL, FALSE);
                    $this->db->update('cad_item_attr attr');
                }
            }
        }
    }


    public function verificar_atributos_default($atributo, $id_item, $grupo_tarifario_item)
    {
        $this->db->select("attr.atributo, attr.atributo_pai, attr.codigo");
        $this->db->where('ci.id_item', $id_item);
        $this->db->join('cad_item ci', 'ci.id_item = attr.id_item', 'inner');
        $query = $this->db->get('cad_item_attr attr');
        $attrs_item =  $query->result();

        $attr_db = [];
        if (!empty($attrs_item)) {
            foreach ($attrs_item as $attr) {
                $attr_db[] = $attr->atributo;
            }
        }

        foreach ($atributo as $attr) {
            if (empty($attr_db) || !in_array($attr['codigo'], $attr_db)) {

                $descricao = "";
                if (isset($attr["dominio"])) {
                    foreach ($attr["dominio"] as $dominio) {
                        if ($dominio["codigo"] == $attr['codigo']) {
                            $descricao = $dominio["descricao"];
                            break;
                        }
                    }
                }

                $dbdata = array(
                    'codigo' => '',
                    'apresentacao' => $attr['nomeApresentacao'],
                    'descricao' => $descricao,
                    'atributo_pai' => '',
                    'atributo' => $attr['codigo'],
                    'obrigatorio' => $attr['obrigatorio'],
                    'id_item' => $id_item,
                    'id_grupo_tarifario' => $grupo_tarifario_item,
                    'id_usuario' => 1,
                    'modalidade' => $attr['modalidade'],
                    'criado_em' =>  \date("Y-m-d H:i:s"),
                    'atualizado_em' => \date("Y-m-d H:i:s"),
                    'ativo' => 1
                );

                $this->db->insert('cad_item_attr', $dbdata);
            }
        }
    }

    public function processarAtributosCondicionados($atributos, $attr_salvo_db, &$attr_default_item)
    {
        foreach ($atributos as $atributo) {

            // Verifica se o atributo é condicionante
            if (isset($atributo['atributoCondicionante']) && $atributo['atributoCondicionante'] == 1) {
                if (isset($atributo['condicionados']) && is_array($atributo['condicionados'])) {
                    $this->processarAtributosCondicionados($atributo['condicionados'], $attr_salvo_db, $attr_default_item);
                }
            }
            // Processa dbdata dentro de atributo
            if (!empty($atributo['atributo']['dbdata']['codigo'])) {

                if (preg_match("/'([^']+)'/", $atributo['descricaoCondicao'], $matches)) {
                    $attrPai = $matches[1];

                    if (!empty($attrPai) && $this->avaliarCondicao($attr_salvo_db[$attrPai] ?? null, $atributo['condicao'])) {
                        if (!in_array($atributo['atributo']['codigo'], $attr_default_item)) {
                            $attr_default_item[] = $atributo['atributo']['codigo'];
                            $this->_attr_estrutura_completa[$atributo['atributo']['codigo']] = [
                                'codigo' => '',
                                'apresentacao' => $atributo['atributo']['nomeApresentacao'],
                                'descricao' => '',
                                'atributo_pai' => isset($atributo['atributo']['codigo_pai']) ? $atributo['atributo']['codigo_pai'] : null,
                                'atributo' => $atributo['atributo']['codigo'],
                                'obrigatorio' => !empty($atributo['atributo']['obrigatorio']) ? $atributo['atributo']['obrigatorio'] : 0,
                                'id_usuario' => 1,
                                'modalidade' => $atributo['atributo']['modalidade'] ?? '',
                                'criado_em' =>  \date("Y-m-d H:i:s"),
                                'atualizado_em' => \date("Y-m-d H:i:s"),
                                'ativo' => 1
                            ];
                        }
                    }
                }
            } else if (isset($atributo['atributo']) && !empty($atributo['atributo'])) {

                if (preg_match("/'([^']+)'/", $atributo['descricaoCondicao'], $matches)) {
                    $attrPai = $matches[1];
                    if (!empty($attrPai) && $this->avaliarCondicao($attr_salvo_db[$attrPai] ?? null, $atributo['condicao'])) {
                        if (!in_array($atributo['atributo']['codigo'], $attr_default_item)) {
                            $attr_default_item[] = $atributo['atributo']['codigo'];
                            $this->_attr_estrutura_completa[$atributo['atributo']['codigo']] = [
                                'codigo' => '',
                                'apresentacao' => $atributo['atributo']['nomeApresentacao'],
                                'descricao' => '',
                                'atributo_pai' => isset($atributo['atributo']['codigo_pai']) ?
                                    $atributo['atributo']['codigo_pai'] : '',
                                'atributo' => $atributo['atributo']['codigo'],
                                'obrigatorio' => !empty($atributo['atributo']['obrigatorio']) ? $atributo['atributo']['obrigatorio'] : 0,
                                'id_usuario' => 1,
                                'modalidade' => $atributo['atributo']['modalidade'] ?? '',
                                'criado_em' =>  \date("Y-m-d H:i:s"),
                                'atualizado_em' => \date("Y-m-d H:i:s"),
                                'ativo' => 1
                            ];
                        }
                    }
                }
            }

            // Processa dbdata no nível atual
            if (!empty($atributo['dbdata'])) {
                if (!in_array($atributo['codigo'], $attr_default_item)) {
                    $attr_default_item[] = $atributo['codigo'];
                    $this->_attr_estrutura_completa[$atributo['codigo']] = [
                        'codigo' => '',
                        'apresentacao' => $atributo['nomeApresentacao'],
                        'descricao' => '',
                        'atributo_pai' => $atributo['codigo_pai'],
                        'atributo' => $atributo['codigo'],
                        'obrigatorio' => !empty($atributo['obrigatorio']) ? $atributo['obrigatorio'] : 0,
                        'id_usuario' => 1,
                        'modalidade' => $atributo['modalidade'],
                        'criado_em' =>  \date("Y-m-d H:i:s"),
                        'atualizado_em' => \date("Y-m-d H:i:s"),
                        'ativo' => 1
                    ];
                }
            }

            // Verifica níveis adicionais de condicionantes
            if (isset($atributo['atributo']) && isset($atributo['atributo']['atributoCondicionante']) && $atributo['atributo']['atributoCondicionante'] == 1) {
                if (isset($atributo['atributo']['condicionados']) && is_array($atributo['atributo']['condicionados'])) {
                    $this->processarAtributosCondicionados($atributo['atributo']['condicionados'], $attr_salvo_db, $attr_default_item);
                }
            }

            // Verifica níveis adicionais de condicionantes
            if (!empty($atributo['listaSubatributos'])) {
                $this->processarAtributosCondicionados($atributo['listaSubatributos'], $attr_salvo_db, $attr_default_item);
            }
        }

        return $attr_default_item;
    }


    public function limpar_atributos_nao_relacionados($id_item, $ncm_proposto, $delete)
    {
        $this->load->model([
            "catalogo/produto_model",
        ]);

        $grupo_tarifario_item = $this->get_grp_tarif_item($id_item);
        $this->_attr_default_item[$id_item] = [];

        if (empty($grupo_tarifario_item)) {
            return;
        }

        $this->limpar_registros_grupo_tarif_divergente($id_item, $grupo_tarifario_item);

        $count = 0;
        $this->_virtual_table = [];

        $this->_attrs = [];
        $this->_attr_estrutura_completa = [];
        if (!empty($ncm_proposto)) {
            $ncm_item = $this->get_required_attrs($this->produto_model->get_attr_ncm($ncm_proposto));

            $array = $this->get_attr($id_item);

            if (empty($array)) {
                return;
            }

            $ncm_item["defaultAttrs"] = $this->filterUniqueAttributes($array);
            $ncm_item["assocAttrs"] = $this->create_assoc_attrs_structure($ncm_item);

            $this->verificar_atributos_default($ncm_item["assocAttrs"], $id_item, $grupo_tarifario_item);
            // $atributos_ncm = $this->filtrarAtributosFilhos($this->_attrs);
            $atributos_ncm = $this->_attrs;
            $attr_default_item = [];
            $attr_salvo_db = [];

            $resultado = $this->filtrarAtributosFilhos($atributos_ncm);
            $atributos_com_pai = [];

            if (!empty($atributos_ncm)) {
                foreach ($atributos_ncm as $registro) {
                    if (empty($registro[2])) {
                        continue;
                    }
                    $atributos_com_pai[] = $registro[2];
                }
                if (!empty($atributos_com_pai)) {
                    $this->db->set('atributo_pai', null);
                    $this->db->from('cad_item_attr');
                    $this->db->where('id_item', $id_item);
                    $this->db->where_not_in('atributo', $atributos_com_pai);
                    $this->db->update('cad_item_attr attr');
                }

                foreach ($atributos_ncm as $registro) {
                    // atributo filho [2] atributo pai  [0]
                    if (
                        isset($registro[2]) && in_array($registro[2], $resultado) &&
                        isset($registro[0]) && !empty($registro[0]) && !empty($registro[2])
                    ) {
                        $this->db->query(" UPDATE cad_item_attr
                            SET atributo_pai = '{$registro[0]}'
                                WHERE id_item = '{$id_item}' 
                                AND atributo = '{$registro[2]}'
                                AND (atributo_pai <> '{$registro[0]}' OR atributo_pai IS NULL)");
                    }
                }

                $ncm_item["defaultAttrs"] = $this->filterUniqueAttributes($array);
                $ncm_item["assocAttrs"] = $this->create_assoc_attrs_structure($ncm_item);
            }

            // Construindo os atributos salvos no banco de dados.
            foreach ($ncm_item["defaultAttrs"] as $i) {
                $attr_salvo_db[$i->atributo] = $i->codigo;
            }

            $attr_default_item = $this->processarAtributosCondicionados($ncm_item["assocAttrs"], $attr_salvo_db, $attr_default_item);

            if (!empty($attr_default_item) && !empty($id_item)) {

                $this->db->select('*');
                $this->db->from('cad_item_attr');
                $this->db->where('id_item', $id_item);
                $this->db->where_not_in('atributo', $attr_default_item);

                $query = $this->db->get();

                if ($query->num_rows() > 0) {
                    $rows = $query->result_array();

                    // Remover o campo 'id' para permitir auto_increment na tabela de log
                    foreach ($rows as &$row) {
                        unset($row['id']);
                    }

                    $this->db->insert_batch('logs_delete_cad_item_attr', $rows);

                    $this->db->where('id_item', $id_item);
                    $this->db->where_not_in('atributo', $attr_default_item);
                    $this->db->delete('cad_item_attr');
                }
            }
            // if (!empty($this->_attr_default_item[$id_item]) && !empty($id_item))
            // {
            //     $this->db->select('*');
            //     $this->db->from('cad_item_attr');
            //     $this->db->where('id_item', $id_item);
            //     $this->db->where_not_in('atributo', $this->_attr_default_item[$id_item]);

            //     $query = $this->db->get();

            //     if ($query->num_rows() > 0) {
            //         $rows = $query->result_array();
            //         $this->db->insert_batch('logs_delete_cad_item_attr', $rows);

            //         $this->db->where('id_item', $id_item);
            //         $this->db->where_not_in('atributo', $this->_attr_default_item[$id_item]);
            //         $this->db->delete('cad_item_attr');
            //     }
            // }
        }
    }

    public function get_entry_by_id_item($id_item)
    {
        $this->db->select('ci.part_number, ci.estabelecimento, ci.id_empresa');
        $this->db->from('cad_item ci');
        $this->db->where('id_item', $id_item);
        $query = $this->db->get();

        return $query->result_array()[0];
    }

    public function update_id_resp_engenharia($id_item, $id_resp_engenharia)
    {
        if (empty($id_item) || empty($id_resp_engenharia)) {
            return;
        }

        $this->db->set('id_resp_engenharia', $id_resp_engenharia);
        $this->db->where('id_item', $id_item);
        $this->db->update('cad_item');
    }

    /**
     * Seta o id do usuário logado na sessão sql
     *
     * @return boolean
     */
    public function set_user_id_sql()
    {
        // Verificar se existe primeiro. Se sim apaga e seta o novo
        $this->db->query("DROP TEMPORARY TABLE IF EXISTS user_update_cad_item_homologacao");

        $this->db->query("SET @user_update_cad_item_homologacao = " . sess_user_id());

        return $this->db->affected_rows() > 0;
    }
}
